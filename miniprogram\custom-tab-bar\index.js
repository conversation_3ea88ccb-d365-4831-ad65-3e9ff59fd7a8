Component({
  data: {
    selected: 0,
    color: "#666666",
    selectedColor: "#0052d9",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        icon: "home"
      },
      {
        pagePath: "/pages/schedule/schedule", 
        text: "活动表",
        icon: "calendar"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的", 
        icon: "user"
      }
    ]
  },
  methods: {
    onTabChange(e) {
      const { value } = e.detail;
      const tab = this.data.list[value];
      
      if (tab) {
        wx.switchTab({
          url: tab.pagePath
        });
        this.setData({
          selected: value
        });
      }
    },
    
    // 支持外部设置selected值
    setSelectedValue(value) {
      this.setData({
        selected: value
      });
    }
  }
}); 