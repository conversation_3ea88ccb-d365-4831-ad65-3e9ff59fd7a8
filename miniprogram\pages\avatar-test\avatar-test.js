// avatar-test.js
// 头像保存功能测试页面

import { showToast, showLoading, hideToast } from '../../utils/toast.js';

Page({
  data: {
    testResults: [],
    isRunning: false,
    currentTest: '',
    avatarUrl: '',
    userInfo: null
  },

  onLoad() {
    console.log('头像测试页面加载');
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    const app = getApp();
    const userInfo = app.getUserInfo();
    this.setData({
      userInfo: userInfo
    });
    console.log('当前用户信息:', userInfo);
  },

  // 测试数据库访问权限
  async testDatabaseAccess() {
    this.addTestResult('开始测试数据库访问权限...');
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'testDatabaseAccess'
        }
      });
      
      console.log('数据库访问测试结果:', res.result);
      
      if (res.result.success) {
        this.addTestResult('✅ 数据库访问权限正常');
        this.addTestResult(`读取测试: ${res.result.readTest.success ? '成功' : '失败'}`);
        if (res.result.writeTest.success) {
          this.addTestResult('✅ 写入测试: 成功');
        } else if (res.result.writeTest.skipped) {
          this.addTestResult('⚠️ 写入测试: 跳过 - ' + res.result.writeTest.reason);
        }
      } else {
        this.addTestResult('❌ 数据库访问权限异常: ' + res.result.message);
        this.addTestResult('错误详情: ' + (res.result.error || '未知错误'));
      }
    } catch (error) {
      console.error('数据库访问测试失败:', error);
      this.addTestResult('❌ 数据库访问测试失败: ' + error.message);
    }
  },

  // 测试头像选择和上传
  async testAvatarUpload() {
    this.addTestResult('开始测试头像选择和上传...');
    
    try {
      // 模拟选择头像
      const chooseResult = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });
      
      if (chooseResult.tempFilePaths && chooseResult.tempFilePaths.length > 0) {
        const tempFilePath = chooseResult.tempFilePaths[0];
        this.addTestResult('✅ 头像选择成功');
        
        // 上传到云存储
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substr(2, 9);
        const cloudPath = `avatars/test_${timestamp}_${randomStr}.jpg`;
        
        this.addTestResult('开始上传到云存储...');
        const uploadResult = await wx.cloud.uploadFile({
          cloudPath,
          filePath: tempFilePath
        });
        
        this.addTestResult('✅ 云存储上传成功');
        this.addTestResult('文件ID: ' + uploadResult.fileID);
        
        this.setData({
          avatarUrl: uploadResult.fileID
        });
        
        return uploadResult.fileID;
      }
    } catch (error) {
      console.error('头像上传测试失败:', error);
      this.addTestResult('❌ 头像上传测试失败: ' + error.message);
      throw error;
    }
  },

  // 测试头像保存到数据库
  async testAvatarSave(avatarUrl) {
    this.addTestResult('开始测试头像保存到数据库...');
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'updateAvatar',
          data: { avatarUrl: avatarUrl }
        }
      });
      
      console.log('头像保存测试结果:', res.result);
      
      if (res.result.success) {
        this.addTestResult('✅ 头像保存成功');
        this.addTestResult('保存的头像URL: ' + res.result.avatarUrl);
      } else {
        this.addTestResult('❌ 头像保存失败: ' + res.result.message);
        this.addTestResult('错误详情: ' + (res.result.error || '未知错误'));
      }
      
      return res.result.success;
    } catch (error) {
      console.error('头像保存测试失败:', error);
      this.addTestResult('❌ 头像保存测试失败: ' + error.message);
      return false;
    }
  },

  // 运行完整测试
  async runFullTest() {
    if (this.data.isRunning) {
      showToast(this, { message: '测试正在进行中...', theme: 'warning' });
      return;
    }

    this.setData({
      isRunning: true,
      testResults: [],
      currentTest: '完整测试'
    });

    showLoading(this, '运行测试中...');

    try {
      // 1. 测试数据库访问权限
      await this.testDatabaseAccess();
      
      // 2. 测试头像上传
      const avatarUrl = await this.testAvatarUpload();
      
      // 3. 测试头像保存
      if (avatarUrl) {
        await this.testAvatarSave(avatarUrl);
      }
      
      this.addTestResult('🎉 完整测试完成');
      
    } catch (error) {
      console.error('完整测试失败:', error);
      this.addTestResult('❌ 完整测试失败: ' + error.message);
    } finally {
      this.setData({
        isRunning: false,
        currentTest: ''
      });
      hideToast(this);
    }
  },

  // 添加测试结果
  addTestResult(message) {
    const timestamp = new Date().toLocaleTimeString();
    const result = `[${timestamp}] ${message}`;
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log('测试结果:', result);
  },

  // 清空测试结果
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  // 单独测试数据库访问
  async onTestDatabase() {
    if (this.data.isRunning) return;
    
    this.setData({
      isRunning: true,
      currentTest: '数据库访问测试'
    });
    
    await this.testDatabaseAccess();
    
    this.setData({
      isRunning: false,
      currentTest: ''
    });
  },

  // 单独测试头像上传
  async onTestUpload() {
    if (this.data.isRunning) return;
    
    this.setData({
      isRunning: true,
      currentTest: '头像上传测试'
    });
    
    try {
      await this.testAvatarUpload();
    } catch (error) {
      // 错误已在testAvatarUpload中处理
    }
    
    this.setData({
      isRunning: false,
      currentTest: ''
    });
  },

  // 测试头像保存（使用当前头像URL）
  async onTestSave() {
    if (this.data.isRunning) return;
    
    if (!this.data.avatarUrl) {
      showToast(this, { message: '请先上传头像', theme: 'warning' });
      return;
    }
    
    this.setData({
      isRunning: true,
      currentTest: '头像保存测试'
    });
    
    await this.testAvatarSave(this.data.avatarUrl);
    
    this.setData({
      isRunning: false,
      currentTest: ''
    });
  }
});
