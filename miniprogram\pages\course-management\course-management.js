/**
 * course-management.js - 课程管理页面
 *
 * 功能概述：
 * 这是健身房管理系统的核心页面之一，负责课程和模板的全生命周期管理
 *
 * 主要功能模块：
 * 1. 课程管理：创建、编辑、删除、上线/下线课程
 * 2. 模板管理：创建、编辑、删除、应用课程模板
 * 3. 数据展示：分页加载、搜索过滤、时间轴展示
 * 4. 状态管理：课程状态切换、预约管理
 *
 * 技术特点：
 * - 使用云函数进行数据操作，确保数据安全性
 * - 实现了复杂的分页和缓存机制
 * - 支持实时搜索和多维度筛选
 * - 采用时间轴模式展示课程，提升用户体验
 *
 * 与您熟悉的技术对比：
 * - 类似于ASP.NET的管理后台页面
 * - 数据操作类似于Entity Framework的CRUD操作
 * - 分页机制类似于SQL Server的分页查询
 */

/**
 * 模块导入区域
 *
 * ES6模块导入语法，类似于C#的using语句
 * 这里导入了数据库操作和UI提示相关的工具函数
 */

// 导入数据库操作工具函数
// getCourseList: 获取课程列表的通用方法
import { getCourseList } from '../../utils/database.js';

// 导入UI提示工具函数
// showToast: 显示消息提示（成功、错误、警告等）
// showLoading: 显示加载动画
// hideToast: 隐藏提示信息
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

/**
 * 工具函数区域
 *
 * 这些函数用于数据格式化，类似于C#中的扩展方法或Java中的工具类
 * 在前端开发中，数据格式化是常见需求，特别是日期时间的处理
 */

/**
 * formatDate: 日期格式化函数
 *
 * 功能：将标准日期字符串转换为用户友好的显示格式
 *
 * @param {string} dateStr - 日期字符串，如 "2024-01-15T10:30:00.000Z"
 * @returns {string} 格式化后的日期，如 "1月15日（星期一）"
 *
 * 实现原理：
 * 1. 创建Date对象解析日期字符串
 * 2. 提取月份和日期信息
 * 3. 通过数组映射获取星期信息
 * 4. 拼接成用户友好的格式
 *
 * 类似技术：
 * - C#: DateTime.ToString("M月d日（dddd）")
 * - Java: SimpleDateFormat或DateTimeFormatter
 * - .NET: string.Format或插值字符串
 */
function formatDate(dateStr) {
  // 防御性编程：检查输入参数是否有效
  if (!dateStr) return '';

  // 创建Date对象：JavaScript的日期处理核心类
  // 类似于C#的DateTime或Java的LocalDateTime
  const date = new Date(dateStr);

  // 获取月份：getMonth()返回0-11，需要+1得到实际月份
  // 这是JavaScript Date对象的特殊设计，需要注意
  const month = date.getMonth() + 1;

  // 获取日期：getDate()返回1-31的日期数字
  const day = date.getDate();

  // 星期映射数组：将数字转换为中文星期
  // getDay()返回0-6，0表示星期日，1表示星期一，以此类推
  const weekMap = ['日','一','二','三','四','五','六'];
  const week = weekMap[date.getDay()];

  // 模板字符串拼接：ES6语法，类似于C#的字符串插值
  // `${变量}` 等价于 C# 的 $"{变量}"
  return `${month}月${day}日（星期${week}）`;
}

/**
 * formatTime: 时间格式化函数
 *
 * 功能：将日期字符串转换为24小时制时间格式
 *
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的时间，如 "14:30"
 *
 * 技术要点：
 * - padStart()方法：字符串补零，确保时间格式统一
 * - toString()转换：将数字转换为字符串进行操作
 */
function formatTime(dateStr) {
  if (!dateStr) return '';

  const date = new Date(dateStr);

  // 获取小时数并补零：padStart(2, '0')确保两位数显示
  // 例如：9 -> "09"，14 -> "14"
  // 类似于C#的ToString("D2")或string.PadLeft(2, '0')
  const hours = date.getHours().toString().padStart(2, '0');

  // 获取分钟数并补零
  const minutes = date.getMinutes().toString().padStart(2, '0');

  // 返回标准时间格式
  return `${hours}:${minutes}`;
}
/**
 * Page对象：小程序页面注册
 *
 * Page()是小程序的核心API，用于注册页面
 * 类似于Vue的export default或React的class Component
 *
 * 与传统开发对比：
 * - ASP.NET: Page类的继承和重写
 * - WinForms: Form类的设计模式
 * - Java: Activity类（Android）或JFrame类（Swing）
 */
Page({
  /**
   * data对象：页面的响应式数据存储
   *
   * 数据绑定机制：
   * - 类似于Vue的data选项
   * - 类似于React的state
   * - 类似于WPF的依赖属性
   * - 类似于Android的ViewModel
   *
   * 数据更新方式：
   * - 使用this.setData()方法更新数据
   * - 数据变化会自动触发页面重新渲染
   * - 支持深度监听和批量更新
   */
  data: {
    /**
     * 选项卡状态管理
     *
     * 设计模式：多级选项卡架构
     * 第一级：课程管理 vs 模板管理
     * 第二级：不同状态的课程筛选
     */

    // 顶部主选项卡：控制显示课程管理还是模板管理
    // 数据类型：string
    // 可选值：'course'(课程管理) | 'template'(模板管理)
    // 默认值：'course' - 默认显示课程管理页面
    // 用途：控制页面主要内容区域的显示
    topActiveTab: 'course',

    // 课程管理子选项卡：控制课程列表的筛选条件
    // 数据类型：string
    // 可选值：
    //   - 'all': 所有有效活动（未结束的活动）
    //   - 'online': 已上线的活动（用户可见可预约）
    //   - 'offline': 未上线的活动（草稿状态）
    //   - 'history': 历史活动（已结束的活动）
    // 默认值：'all' - 默认显示所有有效活动
    courseActiveTab: 'all',

    // 模板管理子选项卡：控制模板列表的筛选条件
    // 数据类型：string


    /**
     * 课程数据管理
     *
     * 数据结构设计：
     * 采用多层数据结构，支持分页、缓存、筛选等复杂需求
     */

    // 课程原始列表：从服务器获取的完整课程数据
    // 数据类型：Array<Object>
    // 数据结构：[{_id, name, startTime, endTime, coach, venue, status, ...}]
    // 用途：作为所有筛选和显示操作的数据源
    courseList: [],

    // 过滤后的课程列表：经过搜索和筛选处理的课程数据
    // 数据类型：Array<Object>
    // 用途：实际在页面上显示的课程数据
    // 更新时机：搜索条件变化、选项卡切换时
    filteredCourseList: [],

    /**
     * 分页控制参数
     *
     * 分页机制：
     * 类似于SQL Server的分页查询或Entity Framework的Skip/Take
     * 支持无限滚动加载，提升大数据量下的用户体验
     */

    // 当前页码：标识当前加载到第几页
    // 数据类型：number
    // 初始值：1 - 从第一页开始
    // 更新时机：每次加载新数据后自增
    coursePage: 1,

    // 每页数据量：控制每次请求的数据条数
    // 数据类型：number
    // 设置原则：平衡加载速度和用户体验
    // 20条：适中的数量，既不会加载过慢，也不会频繁请求
    coursePageSize: 20,

    // 是否还有更多数据：控制是否继续加载下一页
    // 数据类型：boolean
    // true：还有更多数据，可以继续加载
    // false：已加载完所有数据，停止分页请求
    courseHasMore: true,

    // 是否正在加载：防止重复请求的标志位
    // 数据类型：boolean
    // true：正在加载中，阻止新的加载请求
    // false：空闲状态，可以发起新的加载请求
    courseLoading: false,

    // 初始加载状态：标识是否为页面首次加载
    // 数据类型：boolean
    // 用途：控制首次加载时的特殊UI效果（如骨架屏）
    courseInitialLoading: true,

    /**
     * 时间轴模式数据管理
     *
     * 设计理念：
     * 参考微信朋友圈的时间轴设计，将课程按时间顺序展示
     * 支持双向滚动加载：向上加载历史数据，向下加载未来数据
     */

    // 当前可见的课程列表：时间轴模式下实际显示的数据
    // 数据类型：Array<Object>
    // 特点：只包含当前屏幕可见和即将可见的数据
    // 优势：减少DOM节点数量，提升渲染性能
    visibleCourseList: [],

    // 所有未来课程：尚未开始或正在进行的课程
    // 数据类型：Array<Object>
    // 排序：按开始时间升序（最早的在前）
    // 用途：向下滚动时的数据源
    allFutureCourses: [],

    // 所有历史课程：已经结束的课程
    // 数据类型：Array<Object>
    // 排序：按结束时间降序（最近结束的在前）
    // 用途：向上滚动时的数据源
    allHistoryCourses: [],

    /**
     * 加载状态控制
     *
     * 双向加载机制：
     * 支持向上和向下两个方向的数据加载
     * 类似于微信聊天记录的加载方式
     */

    // 顶部加载状态：向上滚动加载历史数据时的状态
    // 数据类型：boolean
    // true：正在加载历史数据
    // false：历史数据加载完成或未在加载
    isLoadingTop: false,

    // 底部加载状态：向下滚动加载更多数据时的状态
    // 数据类型：boolean
    // true：正在加载更多数据
    // false：数据加载完成或未在加载
    isLoadingBottom: false,

    // 历史数据加载完毕标志：是否已加载完所有历史数据
    // 数据类型：boolean
    // true：没有更多历史数据
    // false：还有历史数据可以加载
    noMoreHistory: false,

    // 未来数据加载完毕标志：是否已加载完所有未来数据
    // 数据类型：boolean
    // true：没有更多未来数据
    // false：还有未来数据可以加载
    noMoreFuture: false,

    /**
     * 动画效果控制
     *
     * 用户体验优化：
     * 新加载的内容会有滑入动画，提升视觉体验
     */

    // 闪烁动画索引数组：标记哪些卡片需要播放滑入动画
    // 数据类型：Array<number>
    // 内容：新加载卡片在列表中的索引位置
    // 生命周期：动画播放完成后清空数组
    flashIndexes: [],

    // 时间轴分页大小：每次加载的数据条数
    // 数据类型：number
    // 设置为5：平衡加载速度和滚动体验
    // 较小的数值：更频繁的加载，但滚动更流畅
    pageSize: 5,

    /**
     * 下拉刷新控制
     *
     * 刷新机制：
     * 支持下拉刷新功能，让用户主动更新数据
     */

    // 下拉刷新触发状态：是否正在执行下拉刷新
    // 数据类型：boolean
    // true：正在执行下拉刷新操作
    // false：未在刷新状态
    isRefresherTriggered: false,

    // 下拉刷新状态：详细的刷新状态标识
    // 数据类型：string
    // 可选值：
    //   - 'normal': 正常状态，未进行任何刷新操作
    //   - 'pulling': 正在下拉，但尚未达到刷新阈值
    //   - 'loosing': 已达到刷新阈值，松手即可刷新
    //   - 'loading': 正在执行刷新操作
    refresherStatus: 'normal',

    // 滚动定位控制：scroll-view的scroll-into-view属性
    // 数据类型：string
    // 用途：控制scroll-view滚动到指定id的元素位置
    // 使用场景：回到今天功能，滚动到今天的时间轴位置
    scrollIntoView: '',

    /**
     * 模板管理数据
     *
     * 模板系统：
     * 支持创建课程模板，快速复制生成新课程
     */

    // 模板列表：当前显示的模板数据
    // 数据类型：Array<Object>
    // 数据结构：[{_id, name, coach, venue, duration, description, ...}]
    templateList: [],

    // 可见模板列表：经过筛选和搜索后的模板列表
    // 数据类型：Array<Object>
    // 用途：实际在界面上显示的模板数据
    visibleTemplateList: [],



    // 模板批量模式：是否开启批量操作模式
    // 数据类型：boolean
    // 用途：控制模板卡片的选择状态和批量操作栏的显示
    templateBatchMode: false,

    // 选中的模板ID列表：批量操作时选中的模板
    // 数据类型：Array<string>
    // 用途：记录用户选中的模板，用于批量删除等操作
    selectedTemplateIds: [],

    // 模板搜索关键词：用户输入的搜索内容
    // 数据类型：string
    // 用途：按模板名称、场地等条件筛选模板
    templateSearchKeyword: '',

    // 模板搜索展开状态：搜索框是否展开
    // 数据类型：boolean
    // 用途：控制搜索框的显示/隐藏状态
    templateSearchExpanded: false,

    // 模板滚动定位控制：scroll-view的scroll-into-view属性
    // 数据类型：string
    // 用途：控制模板列表滚动到指定位置
    templateScrollIntoView: '',

    // 模板批量操作确认对话框状态
    // 数据类型：boolean
    // 控制模板批量操作确认对话框的显示/隐藏
    templateBatchConfirmDialogVisible: false,

    // 模板批量操作类型：当前要执行的批量操作
    // 数据类型：string
    // 可选值：'delete'(批量删除)
    templateBatchOperationType: '',

    // 模板批量操作确认倒计时：防止误操作的倒计时
    // 数据类型：number
    // 范围：0-5秒
    // 用途：倒计时结束后才能确认操作
    templateBatchCountdown: 5,

    // 模板批量操作确认对话框内容
    // 数据类型：string
    // 显示在确认对话框中的提示文字
    templateBatchConfirmContent: '',

    // 模板批量操作确认按钮文字
    // 数据类型：string
    // 确认按钮的文字，包含倒计时信息
    templateBatchConfirmBtn: '确认操作',

    // 通用加载状态：模板相关操作的加载状态
    // 数据类型：boolean
    // 用途：模板列表加载、模板操作等场景的loading状态
    loading: false,

    /**
     * 搜索功能数据
     *
     * 搜索机制：
     * 支持实时搜索，按课程名称、教练、场地等维度筛选
     */

    // 搜索关键词：用户输入的搜索内容
    // 数据类型：string
    // 搜索范围：课程名称、教练姓名、场地名称
    // 搜索方式：模糊匹配，实时过滤
    searchValue: '',

    // 搜索框展开状态：控制搜索框的显示/隐藏
    // 数据类型：boolean
    // true: 展开状态，显示完整搜索框
    // false: 收起状态，只显示搜索图标
    // 默认值：false - 默认收起状态，节省空间
    searchExpanded: false,

    // 模板搜索关键词：用户输入的模板搜索内容
    // 数据类型：string
    // 搜索范围：模板名称、场地名称
    // 搜索方式：模糊匹配，实时过滤
    templateSearchValue: '',

    // 模板搜索框展开状态：控制模板搜索框的显示/隐藏
    // 数据类型：boolean
    // true: 展开状态，显示完整搜索框
    // false: 收起状态，只显示搜索图标
    // 默认值：false - 默认收起状态，节省空间
    templateSearchExpanded: false,

    /**
     * 批量操作相关状态
     *
     * 功能：支持批量选择和批量操作活动
     * 设计：提供高效的批量管理能力
     */

    // 批量选择模式：是否开启批量选择模式
    // 数据类型：boolean
    // true: 批量选择模式，显示复选框和批量操作按钮
    // false: 正常模式，显示常规操作按钮
    // 默认值：false - 默认正常模式
    batchMode: false,

    // 已选择的活动ID列表：当前批量选择的活动
    // 数据类型：Array<string>
    // 内容：活动的_id字段组成的数组
    // 用途：批量操作时确定操作对象
    selectedCourseIds: [],

    // 批量操作确认对话框状态
    // 数据类型：boolean
    // 控制批量操作确认对话框的显示/隐藏
    batchConfirmDialogVisible: false,

    // 批量操作类型：当前要执行的批量操作
    // 数据类型：string
    // 可选值：'online'(批量上线) | 'offline'(批量下线) | 'delete'(批量删除)
    batchOperationType: '',

    // 批量操作确认倒计时：防止误操作的倒计时
    // 数据类型：number
    // 范围：0-5秒
    // 用途：倒计时结束后才能确认操作
    batchCountdown: 5,

    // 批量操作确认对话框内容
    // 数据类型：string
    // 显示在确认对话框中的提示文字
    batchConfirmContent: '',

    // 批量操作确认按钮文字
    // 数据类型：string
    // 确认按钮的文字，包含倒计时信息
    batchConfirmBtn: '确认操作',

    /**
     * 选项卡配置数据
     *
     * 配置化设计：
     * 将选项卡的标签和值定义为配置数据，便于维护和扩展
     */

    // 课程选项卡配置：定义课程管理的所有选项卡
    // 数据类型：Array<Object>
    // 数据结构：[{label: '显示文本', value: '内部值'}]
    // 设计优势：便于国际化、动态配置、统一管理
    courseTabList: [
      { label: '有效活动', value: 'all' },     // 所有未结束的活动
      { label: '已上线', value: 'online' },    // 用户可见的活动
      { label: '未上线', value: 'offline' },   // 草稿状态的活动
      { label: '历史活动', value: 'history' }   // 已结束的活动
    ],

    /**
     * 缓存机制数据
     *
     * 性能优化：
     * 为每个选项卡缓存数据，避免重复请求
     * 类似于浏览器缓存或Redis缓存的概念
     */

    // 课程选项卡缓存：为每个tab维护独立的数据缓存
    // 数据类型：Object
    // 结构：{
    //   [tabValue]: {
    //     list: Array,     // 该tab的课程列表
    //     page: number,    // 该tab的当前页码
    //     hasMore: boolean // 该tab是否还有更多数据
    //   }
    // }
    // 优势：
    // 1. 减少网络请求：切换tab时优先使用缓存
    // 2. 提升用户体验：快速响应tab切换
    // 3. 保持状态：记住每个tab的滚动位置和加载状态
    courseTabCache: {
      all: { list: [], page: 1, hasMore: true },      // 有效活动缓存
      online: { list: [], page: 1, hasMore: true },   // 已上线活动缓存
      offline: { list: [], page: 1, hasMore: true },  // 未上线活动缓存
      history: { list: [], page: 1, hasMore: true }   // 历史活动缓存
    },

    /**
     * 移除学员确认对话框数据
     *
     * 功能：管理员为学员取消预约的确认机制
     * 特点：带有倒计时的确认按钮，防止误操作
     * 权限：仅管理员可用，不受时间限制
     */

    // 移除学员对话框显示状态：控制确认对话框的显示/隐藏
    // 数据类型：boolean
    // true：显示对话框，false：隐藏对话框
    // 默认值：false - 默认隐藏状态
    removeStudentDialogVisible: false,

    // 移除学员对话框内容：动态显示的确认信息
    // 数据类型：string
    // 内容包括：学员姓名、课程信息、倒计时提示等
    // 格式示例："确定要为学员"张三"取消预约吗？此操作不受时间限制。\n\n倒计时：5秒"
    removeStudentDialogContent: '',

    // 移除学员对话框确认按钮文字：带倒计时的按钮文字
    // 数据类型：string
    // 倒计时期间：显示"确认移除(5)"格式
    // 倒计时结束：显示"确认移除"
    // 默认值：包含初始倒计时的文字
    removeStudentDialogConfirmBtn: '确认移除(5)',

    // 移除学员倒计时秒数：防止误操作的安全倒计时
    // 数据类型：number
    // 初始值：5 - 用户需要等待5秒才能确认移除
    // 递减规则：每秒减1，直到为0
    // 重置时机：每次打开移除确认对话框时重置为5
    removeStudentCountdown: 5,

    // 移除学员倒计时定时器：控制倒计时的定时器对象
    // 数据类型：Timer | null
    // 创建时机：打开移除确认对话框时创建
    // 清除时机：倒计时结束、用户取消、确认移除时清除
    // 用途：每秒更新倒计时数值和按钮文字
    removeStudentCountdownTimer: null,

    // 待移除的学员数据：存储即将被移除的学员和课程信息
    // 数据类型：Object | null
    // 数据结构：{
    //   courseId: string,     // 课程ID
    //   studentOpenid: string, // 学员openid
    //   studentName: string,   // 学员姓名
    //   bookingId: string     // 预约记录ID
    // }
    // null状态：没有学员等待移除时为null
    removeStudentData: null,

    /**
     * 用户权限相关数据
     *
     * 功能：控制不同功能的显示和访问权限
     * 用途：根据用户角色显示或隐藏特定功能
     */

    // 是否为管理员：控制管理员专用功能的显示
    // 数据类型：boolean
    // true：用户具有管理员权限，显示管理员功能
    // false：用户不是管理员，隐藏管理员功能
    // 用途：控制移除学员按钮等管理员专用功能的显示
    isAdmin: false,
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 生命周期对比：
   * - 小程序：onLoad → onShow → onReady
   * - Vue：created → mounted
   * - React：constructor → componentDidMount
   * - ASP.NET：Page_Load事件
   * - WinForms：Form_Load事件
   * - Android：onCreate()
   *
   * 执行时机：
   * 页面首次加载时调用，整个页面生命周期中只执行一次
   *
   * 主要职责：
   * 1. 初始化页面状态
   * 2. 加载必要的数据
   * 3. 设置页面配置
   *
   * async/await说明：
   * 使用异步函数处理数据加载，避免阻塞UI线程
   */
  async onLoad() {
    /**
     * TabBar隐藏处理
     *
     * 业务逻辑：
     * 课程管理是管理员专用页面，不需要显示底部导航栏
     * 通过设置selectedValue为-1来隐藏TabBar高亮
     *
     * 防御性编程：
     * 使用typeof检查和条件判断，确保方法存在才调用
     * 避免在某些环境下出现方法不存在的错误
     */
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // getTabBar(): 获取自定义TabBar组件实例
      // setSelectedValue(-1): 设置选中值为-1，表示不高亮任何tab
      this.getTabBar().setSelectedValue(-1);
    }

    /**
     * 数据初始化流程
     *
     * 加载顺序说明：
     * 1. 显示加载提示：提升用户体验，告知正在加载
     * 2. 并行加载数据：课程列表和模板列表同时加载
     * 3. 隐藏加载提示：数据加载完成后清除loading状态
     *
     * 错误处理：
     * 如果某个数据加载失败，不会影响其他数据的加载
     * 每个加载方法内部都有独立的错误处理机制
     */

    // 显示全局加载提示
    showLoading(this, '加载中...');

    // 检查用户权限
    await this.checkUserPermissions();

    // 加载课程列表：reset=true表示重新加载，清空现有数据
    await this.loadCourseListPaged(true);

    // 加载模板列表：为模板管理tab准备数据
    await this.loadTemplateList();

    // 隐藏加载提示
    hideToast(this);
  },

  /**
   * checkUserPermissions: 检查用户权限
   *
   * 功能概述：
   * 检查当前用户是否具有管理员权限
   * 设置isAdmin变量控制管理员功能的显示
   *
   * 权限来源：
   * 从全局数据或本地存储中获取用户信息
   * 检查用户角色是否包含"管理员"
   */
  async checkUserPermissions() {
    try {
      // 从全局数据获取用户信息
      const app = getApp();
      let userInfo = app.globalData.userInfo;

      // 如果全局数据中没有用户信息，尝试从本地存储获取
      if (!userInfo) {
        try {
          userInfo = wx.getStorageSync('userInfo');
        } catch (error) {
          console.log('获取本地用户信息失败:', error);
        }
      }

      // 检查用户是否为管理员
      let isAdmin = false;
      if (userInfo && userInfo.roles && Array.isArray(userInfo.roles)) {
        isAdmin = userInfo.roles.includes('管理员');
      }

      // 更新页面数据
      this.setData({ isAdmin });

      console.log('用户权限检查完成:', { isAdmin, userRoles: userInfo?.roles });
    } catch (error) {
      console.error('检查用户权限失败:', error);
      // 出错时默认设置为非管理员
      this.setData({ isAdmin: false });
    }
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 触发时机：
   * 1. 页面首次显示（在onLoad之后）
   * 2. 从其他页面返回到当前页面
   * 3. 从后台切换到前台
   * 4. 从子页面返回父页面
   *
   * 与onLoad的区别：
   * - onLoad：只在页面创建时调用一次
   * - onShow：每次页面显示都会调用
   *
   * 使用场景：
   * 适合处理需要每次显示都更新的数据
   * 例如：从编辑页面返回后刷新列表数据
   */
  onShow() {
    /**
     * TabBar状态重置
     *
     * 原因：
     * 从其他页面返回时，TabBar可能会恢复高亮状态
     * 需要重新设置为不高亮状态
     */
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    /**
     * 数据自动刷新
     *
     * 业务场景：
     * 1. 从课程编辑页面返回：需要刷新课程列表显示最新数据
     * 2. 从课程详情页面返回：可能课程状态发生变化
     * 3. 从其他管理页面返回：确保数据一致性
     *
     * 性能考虑：
     * 虽然每次显示都刷新会增加网络请求
     * 但确保了数据的实时性，对管理页面来说是必要的
     */
    this.refreshData();
  },

  /**
   * refreshData: 数据刷新方法
   *
   * 功能：根据当前选中的tab刷新对应的数据
   *
   * 设计模式：策略模式
   * 根据不同的topActiveTab执行不同的刷新策略
   *
   * 异步处理：
   * 使用async/await确保数据加载完成后再进行后续操作
   *
   * 错误处理：
   * 使用try-catch捕获异常，避免程序崩溃
   * 向用户显示友好的错误提示
   */
  async refreshData() {
    try {
      // 根据当前活跃的顶部tab决定刷新哪种数据
      if (this.data.topActiveTab === 'course') {
        // 课程管理tab：刷新课程列表
        // reset=true：清空现有数据，重新加载第一页
        await this.loadCourseListPaged(true);
      } else {
        // 模板管理tab：刷新模板列表
        await this.loadTemplateList();
      }
    } catch (error) {
      /**
       * 错误处理最佳实践
       *
       * 1. 记录错误日志：便于开发者调试和问题排查
       * 2. 用户友好提示：不暴露技术细节，显示易懂的错误信息
       * 3. 程序继续运行：错误不应该导致页面崩溃
       */
      console.error('刷新数据失败:', error);
      showToast(this, { message: '刷新数据失败', theme: 'error' });
    }
  },

  /**
   * onPullDownRefresh: 下拉刷新事件处理函数
   *
   * 触发条件：
   * 用户在页面顶部下拉时触发
   * 需要在页面配置文件中启用：enablePullDownRefresh: true
   *
   * 实现流程：
   * 1. 用户下拉页面
   * 2. 系统检测到下拉手势
   * 3. 触发onPullDownRefresh事件
   * 4. 执行数据刷新逻辑
   * 5. 调用wx.stopPullDownRefresh()结束刷新状态
   *
   * 用户体验：
   * 提供主动刷新数据的方式，增强用户控制感
   */
  async onPullDownRefresh() {
    // 执行数据刷新
    await this.refreshData();

    // 停止下拉刷新的loading动画
    // 必须调用此方法，否则刷新动画会一直显示
    wx.stopPullDownRefresh();
  },

  /**
   * loadCourseListPaged: 分页加载课程列表的核心方法
   *
   * 功能概述：
   * 这是课程管理页面最重要的数据加载方法，负责从云数据库获取课程数据
   * 支持分页加载、状态筛选、缓存管理等复杂功能
   *
   * 技术特点：
   * 1. 异步处理：使用async/await避免阻塞UI
   * 2. 防重复请求：通过loading状态防止重复调用
   * 3. 分页机制：支持无限滚动加载
   * 4. 状态筛选：根据tab筛选不同状态的课程
   * 5. 数据格式化：统一处理时间格式和显示字段
   *
   * @param {boolean} reset - 是否重置列表（清空现有数据重新加载）
   *   - true: 清空现有数据，从第一页开始加载（用于刷新）
   *   - false: 在现有数据基础上加载下一页（用于分页）
   *
   * 与您熟悉的技术对比：
   * - 类似于C#的async Task<List<Course>> LoadCoursesAsync(bool reset)
   * - 类似于Java的CompletableFuture<List<Course>> loadCourses(boolean reset)
   * - 分页逻辑类似于SQL Server的OFFSET...FETCH或MySQL的LIMIT...OFFSET
   */
  async loadCourseListPaged(reset = false) {
    /**
     * 防重复请求检查
     *
     * 条件判断逻辑：
     * 1. this.data.courseLoading: 如果正在加载中，直接返回
     * 2. !this.data.courseHasMore && !reset: 如果没有更多数据且不是重置操作，直接返回
     *
     * 防御性编程：
     * 避免用户快速操作导致的重复网络请求
     * 类似于C#中的lock机制或Java中的synchronized
     */
    if (this.data.courseLoading || (!this.data.courseHasMore && !reset)) return;

    /**
     * 设置加载状态
     *
     * 目的：
     * 1. 防止重复请求
     * 2. 触发UI显示loading动画
     * 3. 禁用相关操作按钮
     */
    this.setData({ courseLoading: true });

    /**
     * 异步数据加载的标准错误处理模式
     *
     * try-catch-finally结构：
     * - try: 执行主要的数据加载逻辑
     * - catch: 处理可能出现的错误（网络错误、数据错误等）
     * - finally: 无论成功失败都要执行的清理工作
     */
    try {
      /**
       * 页码计算逻辑
       *
       * 三元运算符：condition ? value1 : value2
       * - reset为true: 从第1页开始加载
       * - reset为false: 加载当前页码的下一页
       *
       * 使用场景：
       * - 下拉刷新: reset=true
       * - 滚动加载更多: reset=false
       * - 切换tab: reset=true
       */
      let page = reset ? 1 : this.data.coursePage;

      /**
       * 重置操作的数据清理
       *
       * 重置场景：
       * 1. 用户下拉刷新
       * 2. 切换筛选tab
       * 3. 搜索条件变化
       * 4. 页面初始加载
       */
      if (reset) {
        this.setData({
          // 清空课程原始列表：移除所有现有数据
          courseList: [],

          // 清空过滤后的列表：确保搜索结果也被清空
          // 重要：如果不清空这个列表，可能会显示旧的搜索结果
          filteredCourseList: [],

          // 重置分页状态：假设还有更多数据可以加载
          courseHasMore: true,

          // 重置页码：从第一页开始
          coursePage: 1,

          // 初始加载状态：只有第一页加载时才显示初始loading
          // 用于控制骨架屏等特殊UI效果
          courseInitialLoading: page === 1
        });
      }

      /**
       * 构建筛选参数
       *
       * 业务逻辑：根据当前选中的tab构建不同的查询条件
       * 这里体现了策略模式的设计思想
       */

      // 声明筛选变量：用于传递给云函数的查询参数
      let status, history;

      // 获取当前活跃的课程tab
      const tab = this.data.courseActiveTab;

      /**
       * Tab筛选逻辑映射
       *
       * 业务规则说明：
       * - online: 已上线且未结束的活动（用户可见可预约）
       * - offline: 未上线且未结束的活动（草稿状态）
       * - history: 所有已结束的活动（不区分上线状态）
       * - all: 所有未结束的活动（不区分上线状态）
       *
       * 数据库查询逻辑：
       * - status字段：控制课程的上线状态
       * - history字段：控制是否查询历史数据
       */
      if (tab === 'online') {
        status = 'online';    // 筛选状态为"已上线"的课程
        history = false;      // 只查询未结束的活动
      } else if (tab === 'offline') {
        status = 'offline';   // 筛选状态为"未上线"的课程
        history = false;      // 只查询未结束的活动
      } else if (tab === 'history') {
        // status不设置：不限制上线状态，包含所有历史活动
        history = true;       // 只查询已结束的活动
      } else if (tab === 'all') {
        // status不设置：不限制上线状态，包含已上线和未上线
        history = false;      // 只查询未结束的活动
      }
      /**
       * 调用云函数获取课程数据
       *
       * 云函数调用机制：
       * 小程序通过wx.cloud.callFunction调用服务端的云函数
       * 类似于传统Web开发中的AJAX请求或移动端的HTTP API调用
       *
       * 与其他技术对比：
       * - Web: fetch('/api/courses', {method: 'POST', body: JSON.stringify(data)})
       * - C#: await httpClient.PostAsync("/api/courses", content)
       * - Java: restTemplate.postForObject("/api/courses", request, CourseResponse.class)
       * - Android: Retrofit接口调用
       *
       * 云函数优势：
       * 1. 安全性：数据库操作在服务端执行，避免敏感信息泄露
       * 2. 性能：服务端处理复杂逻辑，减少客户端计算压力
       * 3. 一致性：统一的数据处理逻辑，避免客户端差异
       */
      const res = await wx.cloud.callFunction({
        /**
         * name: 云函数名称
         * 'adminManagement': 管理员相关操作的云函数
         *
         * 云函数架构：
         * 按功能模块划分不同的云函数，便于维护和权限控制
         * - adminManagement: 管理员操作（课程管理、用户管理等）
         * - userManagement: 用户操作（预约、查询等）
         * - bookingManagement: 预约相关操作
         */
        name: 'adminManagement',

        /**
         * data: 传递给云函数的参数
         *
         * 参数结构设计：
         * 采用action模式，一个云函数处理多种操作
         * 类似于RESTful API的设计思想
         */
        data: {
          /**
           * action: 操作类型标识
           * 'getCourseListPaged': 获取分页课程列表
           *
           * 其他可能的action：
           * - 'createCourse': 创建课程
           * - 'updateCourse': 更新课程
           * - 'deleteCourse': 删除课程
           * - 'updateCourseStatus': 更新课程状态
           */
          action: 'getCourseListPaged',

          /**
           * data: 具体的业务参数
           *
           * 参数说明：
           * - page: 页码，从1开始
           * - pageSize: 每页数据量，控制单次请求的数据量
           * - status: 课程状态筛选（可选）
           * - history: 是否查询历史数据（可选）
           */
          data: {
            page,                           // 当前页码
            pageSize: this.data.coursePageSize,  // 每页数据量（通常为20）
            status,                         // 课程状态（online/offline/undefined）
            history                         // 是否历史数据（true/false/undefined）
          }
        }
      });

      /**
       * 云函数返回结果处理
       *
       * 标准返回格式：
       * {
       *   result: {
       *     success: boolean,    // 操作是否成功
       *     message: string,     // 错误信息或成功提示
       *     data: Array         // 实际的课程数据
       *   }
       * }
       *
       * 错误处理策略：
       * 1. 检查success字段判断操作是否成功
       * 2. 失败时显示错误信息并提前返回
       * 3. 成功时继续处理数据
       */
      if (!res.result.success) {
        // 显示错误提示：使用服务端返回的错误信息，或默认提示
        showToast(this, {
          message: res.result.message || '加载失败',
          theme: 'error'
        });
        return; // 提前返回，不执行后续的数据处理逻辑
      }

      /**
       * 提取课程数据
       *
       * 防御性编程：
       * 使用 || [] 确保即使服务端返回null或undefined，也能得到空数组
       * 避免后续的数组操作出现错误
       */
      let list = res.result.data || [];
      /**
       * 数据格式化处理
       *
       * 目的：将服务端返回的原始数据转换为前端显示所需的格式
       * 主要处理：时间格式化、UI状态初始化、显示字段计算
       *
       * Array.map()方法：
       * - 遍历数组中的每个元素
       * - 对每个元素执行转换函数
       * - 返回新的数组，不修改原数组
       * - 类似于C#的LINQ Select方法或Java的Stream.map()
       */
      list = list.map(item => {
        /**
         * 初始化格式化字段
         *
         * 字段说明：
         * - formattedDate: 用户友好的日期格式，如"1月15日(星期一)"
         * - formattedTime: 开始时间，如"14:30"
         * - timelineDate: 时间轴显示格式，如"2024年1月15日（周一）"
         */
        let formattedDate = '', formattedTime = '', timelineDate = '';

        /**
         * 开始时间格式化处理
         *
         * 防御性编程：先检查startTime是否存在
         * 避免对null或undefined调用Date构造函数
         */
        if (item.startTime) {
          /**
           * 创建Date对象
           *
           * JavaScript Date对象：
           * - 可以解析多种日期格式
           * - ISO 8601格式：2024-01-15T14:30:00.000Z
           * - 时间戳格式：1705329000000
           * - 字符串格式：'2024-01-15 14:30:00'
           */
          const date = new Date(item.startTime);

          /**
           * 提取日期组件
           *
           * Date对象的getter方法：
           * - getFullYear(): 获取四位数年份
           * - getMonth(): 获取月份（0-11，需要+1）
           * - getDate(): 获取日期（1-31）
           * - getDay(): 获取星期（0-6，0表示星期日）
           * - getHours(): 获取小时（0-23）
           * - getMinutes(): 获取分钟（0-59）
           */
          const year = date.getFullYear();
          const month = date.getMonth() + 1;  // 注意：月份需要+1
          const day = date.getDate();

          /**
           * 星期映射数组
           *
           * 设计原因：
           * getDay()返回数字0-6，需要转换为中文星期
           * 数组索引正好对应getDay()的返回值
           */
          const weekMap = ['日','一','二','三','四','五','六'];
          const week = weekMap[date.getDay()];

          /**
           * 生成用户友好的日期格式
           *
           * 模板字符串语法：`${变量}`
           * 等价于字符串拼接：month + '月' + day + '日(星期' + week + ')'
           * 但模板字符串更简洁、可读性更好
           */
          formattedDate = `${month}月${day}日(星期${week})`;

          /**
           * 生成时间轴专用的日期格式
           *
           * 设计考虑：
           * 时间轴需要更完整的日期信息，包含年份
           * 参考my-bookings页面的格式，保持一致性
           * 使用"周"而不是"星期"，更简洁
           */
          timelineDate = `${year}年${month}月${day}日（周${week}）`;

          /**
           * 格式化开始时间
           *
           * padStart()方法：字符串补零
           * - 第一个参数：目标长度
           * - 第二个参数：用于填充的字符
           * - 例如：9.toString().padStart(2, '0') → "09"
           *
           * 目的：确保时间格式统一为HH:MM
           */
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          formattedTime = `${hours}:${minutes}`;
        }

        /**
         * 结束时间格式化处理
         *
         * 逻辑与开始时间类似，但只需要时分格式
         * 不需要日期信息，因为通常课程在同一天内结束
         */
        let endTimeStr = '';
        if (item.endTime) {
          const date = new Date(item.endTime);
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          endTimeStr = `${hours}:${minutes}`;
        }

        /**
         * 返回增强后的课程对象
         *
         * 扩展运算符语法：...item
         * - 展开原对象的所有属性
         * - 类似于C#的对象初始化器或Java的Builder模式
         * - 后面的属性会覆盖前面的同名属性
         *
         * 新增字段说明：
         * - formattedDate: 格式化的日期字符串
         * - formattedTime: 格式化的开始时间
         * - endTimeStr: 格式化的结束时间
         * - timelineDate: 时间轴专用的日期格式
         * - collapseValue: 折叠面板状态数组，初始为空（全部收起）
         */
        return {
          ...item,                    // 保留原对象的所有属性
          formattedDate,              // 添加格式化日期
          formattedTime,              // 添加格式化开始时间
          endTimeStr,                 // 添加格式化结束时间
          timelineDate,               // 添加时间轴日期格式
          encodedTimelineDate: encodeURIComponent(timelineDate), // 添加编码后的时间轴日期，用于id
          collapseValue: []           // 初始化折叠面板状态（全部收起）
        };
      });

      /**
       * 数据排序处理
       *
       * 排序策略：根据不同的tab类型采用不同的排序方向
       * 设计理念：符合用户的使用习惯和心理预期
       */
      list.sort((a, b) => {
        /**
         * 时间戳转换
         *
         * getTime()方法：
         * - 返回从1970年1月1日UTC时间到指定日期的毫秒数
         * - 便于进行数值比较和排序
         * - 类似于C#的DateTime.Ticks或Java的System.currentTimeMillis()
         *
         * 防御性处理：
         * 如果startTime不存在，使用0作为默认值
         * 确保排序不会因为null值而出错
         */
        const timeA = a.startTime ? new Date(a.startTime).getTime() : 0;
        const timeB = b.startTime ? new Date(b.startTime).getTime() : 0;

        /**
         * 排序方向决策
         *
         * 业务逻辑：
         * 1. 历史活动：最近结束的在前（降序）
         *    - 用户更关心最近的活动
         *    - 便于查看最新的活动记录
         *
         * 2. 其他活动：最早开始的在前（升序）
         *    - 按时间顺序安排活动
         *    - 便于管理员按计划查看
         *
         * 排序返回值：
         * - 负数：a排在b前面
         * - 正数：b排在a前面
         * - 0：保持原有顺序
         */
        if (this.data.courseActiveTab === 'history') {
          return timeB - timeA; // 历史活动：最近的在前（降序）
        } else {
          return timeA - timeB; // 其他活动：最早的在前（升序）
        }
      });

      /**
       * 缓存机制实现
       *
       * 缓存策略：
       * 为每个tab维护独立的数据缓存，提升用户体验
       * 类似于浏览器缓存或Redis缓存的概念
       *
       * 优势：
       * 1. 减少网络请求：切换tab时优先使用缓存
       * 2. 提升响应速度：立即显示缓存数据
       * 3. 保持状态：记住每个tab的滚动位置和加载进度
       */
      const tabCacheKey = this.data.courseActiveTab;

      /**
       * 数据合并逻辑
       *
       * 两种模式：
       * 1. reset=true：使用新数据替换缓存（刷新场景）
       * 2. reset=false：将新数据追加到现有数据（分页场景）
       *
       * concat()方法：
       * - 连接两个数组，返回新数组
       * - 不修改原数组，符合函数式编程原则
       * - 类似于C#的List.AddRange()或Java的List.addAll()
       */
      const newList = reset ? list : this.data.courseList.concat(list);

      /**
       * 批量状态更新
       *
       * setData()的回调机制：
       * 第二个参数是回调函数，在数据更新完成后执行
       * 确保数据更新完成后再进行后续操作
       */
      this.setData({
        /**
         * 更新主要数据状态
         */
        courseList: newList,                    // 更新课程列表

        /**
         * 分页状态更新
         *
         * courseHasMore判断逻辑：
         * 如果返回的数据量等于请求的页面大小，说明可能还有更多数据
         * 如果返回的数据量小于页面大小，说明已经是最后一页
         */
        courseHasMore: list.length === this.data.coursePageSize,

        /**
         * 页码递增
         *
         * 为下次分页请求准备页码
         * page + 1：下次请求时加载下一页
         */
        coursePage: page + 1,

        /**
         * 缓存更新
         *
         * 动态属性名语法：[`courseTabCache.${tabCacheKey}`]
         * - 使用模板字符串生成属性路径
         * - 支持嵌套对象的属性更新
         * - 类似于lodash的set方法
         *
         * 缓存内容：
         * - list: 该tab的完整数据列表
         * - page: 该tab的当前页码
         * - hasMore: 该tab是否还有更多数据
         */
        [`courseTabCache.${tabCacheKey}`]: {
          list: newList,
          page: page + 1,
          hasMore: list.length === this.data.coursePageSize
        }
      }, () => {
        /**
         * 数据更新完成后的回调处理
         *
         * _filterAndSetCourses()：
         * 应用搜索过滤条件，更新显示列表
         * 确保搜索功能在数据更新后仍然有效
         */
        this._filterAndSetCourses();
      });
    } finally {
      /**
       * 清理工作
       *
       * finally块：无论try块是否成功，都会执行
       * 用于清理资源、重置状态等必要操作
       *
       * 状态重置：
       * - courseLoading: 结束加载状态，允许新的请求
       * - courseInitialLoading: 结束初始加载状态，隐藏骨架屏
       */
      this.setData({
        courseLoading: false,           // 结束加载状态
        courseInitialLoading: false     // 结束初始加载状态
      });
    }
  },

  /**
   * onReachBottom: 页面滚动到底部的事件处理函数
   *
   * 触发时机：
   * 当页面滚动到底部时，小程序会自动调用此方法
   * 需要在页面配置文件中设置onReachBottomDistance来控制触发距离
   *
   * 功能：实现无限滚动加载更多数据
   *
   * 与其他技术对比：
   * - Web: window.onscroll + 滚动位置计算
   * - Android: RecyclerView.OnScrollListener
   * - iOS: UIScrollViewDelegate.scrollViewDidScroll
   * - Vue: v-infinite-scroll指令
   * - React: react-infinite-scroll-component
   */
  onReachBottom() {
    /**
     * 条件检查：只有在课程管理tab时才触发分页加载
     *
     * 原因：
     * 1. 模板管理tab可能有不同的加载逻辑
     * 2. 避免在错误的tab下触发不必要的请求
     * 3. 保持功能的独立性和可控性
     */
    if (this.data.topActiveTab === 'course') {
      /**
       * 调用分页加载方法
       *
       * 参数说明：
       * 不传参数，默认reset=false，表示追加加载下一页数据
       * 而不是重新加载第一页数据
       */
      this.loadCourseListPaged();
    }
  },

  /**
   * onCourseScrollToLower: 时间轴模式下的滚动到底部处理
   *
   * 设计理念：
   * 参考微信朋友圈的时间轴设计，实现虚拟滚动和分页加载
   * 只显示当前可见的数据，减少DOM节点数量，提升性能
   *
   * 与onReachBottom的区别：
   * - onReachBottom: 页面级别的滚动事件，简单的分页加载
   * - onCourseScrollToLower: 组件级别的滚动事件，复杂的时间轴分页
   *
   * 技术特点：
   * 1. 虚拟滚动：只渲染可见区域的数据
   * 2. 双向加载：支持向上和向下加载数据
   * 3. 动画效果：新加载的内容有滑入动画
   * 4. 状态管理：精确控制加载状态和边界条件
   */
  onCourseScrollToLower() {
    /**
     * 防重复加载检查
     *
     * 如果正在加载底部数据，直接返回
     * 避免用户快速滚动时触发多次加载请求
     */
    if (this.data.isLoadingBottom) return;

    /**
     * 解构赋值获取必要数据
     *
     * ES6解构语法：const { a, b, c } = object
     * 等价于：
     * const a = this.data.a;
     * const b = this.data.b;
     * const c = this.data.c;
     *
     * 优势：代码更简洁，减少重复的this.data访问
     */
    const { visibleCourseList, pageSize, courseActiveTab } = this.data;

    /**
     * 获取当前显示列表的最后一项
     *
     * 数组索引计算：
     * - length - 1: 获取数组最后一个元素的索引
     * - 例如：[a, b, c].length = 3，最后元素索引为2
     */
    const last = visibleCourseList[visibleCourseList.length - 1];

    /**
     * 根据当前tab类型执行不同的加载策略
     *
     * 业务逻辑分支：
     * - history tab: 加载更多历史数据
     * - 其他tab: 加载更多未来数据
     */
    if (courseActiveTab === 'history') {
      /**
       * 历史活动tab的加载逻辑
       */

      // 获取所有历史课程数据
      const { allHistoryCourses } = this.data;

      /**
       * 查找当前最后一项在完整数据中的位置
       *
       * findIndex()方法：
       * - 查找满足条件的第一个元素的索引
       * - 找不到时返回-1
       * - 类似于C#的List.FindIndex()或Java的List.indexOf()
       */
      const lastIdx = allHistoryCourses.findIndex(c => c._id === last._id);

      /**
       * 边界条件检查
       *
       * 两种情况表示没有更多数据：
       * 1. lastIdx === -1: 当前项不在完整数据中（数据异常）
       * 2. lastIdx === allHistoryCourses.length - 1: 已经是最后一项
       */
      if (lastIdx === -1 || lastIdx === allHistoryCourses.length - 1) {
        this.setData({ noMoreHistory: true });
        return; // 提前返回，不执行加载逻辑
      }

      /**
       * 设置加载状态
       *
       * 状态更新：
       * - isLoadingBottom: true - 开始底部加载
       * - noMoreHistory: false - 重置"没有更多"状态
       */
      this.setData({ isLoadingBottom: true, noMoreHistory: false });

      /**
       * 延迟加载模拟
       *
       * setTimeout的作用：
       * 1. 模拟网络请求的延迟时间
       * 2. 给用户足够的视觉反馈时间
       * 3. 避免加载过快导致的闪烁效果
       *
       * 600ms延迟：
       * - 不会让用户感觉太慢
       * - 足够显示loading动画
       * - 符合用户对网络请求的心理预期
       */
      setTimeout(() => {
        /**
         * 数据切片获取
         *
         * slice()方法：
         * - 提取数组的一部分，返回新数组
         * - 参数：(开始索引, 结束索引)
         * - 结束索引不包含在结果中
         *
         * 计算逻辑：
         * - 开始：lastIdx + 1（下一项开始）
         * - 结束：lastIdx + 1 + pageSize（加载指定数量）
         */
        const next = allHistoryCourses.slice(lastIdx + 1, lastIdx + 1 + pageSize);

        /**
         * 更新显示数据和状态
         */
        this.setData({
          /**
           * 追加新数据到可见列表
           *
           * concat()方法：连接数组，不修改原数组
           * 将新加载的数据追加到现有的可见列表末尾
           */
          visibleCourseList: visibleCourseList.concat(next),

          // 结束底部加载状态
          isLoadingBottom: false,

          /**
           * 设置动画索引
           *
           * Array.from()方法：
           * - 从类数组对象创建新数组
           * - 第一个参数：{length: n} 创建长度为n的数组
           * - 第二个参数：映射函数，(value, index) => newValue
           *
           * 目的：为新加载的每个项目生成对应的索引
           * 用于触发滑入动画效果
           */
          flashIndexes: Array.from({length: next.length}, (_, i) => visibleCourseList.length + i),

          /**
           * 更新"没有更多"状态
           *
           * 判断逻辑：
           * 如果下次加载的起始位置超过或等于总数据长度
           * 说明已经没有更多历史数据了
           */
          noMoreHistory: lastIdx + 1 + pageSize >= allHistoryCourses.length
        });

        /**
         * 清除动画效果
         *
         * 1000ms后清空flashIndexes数组
         * 移除滑入动画效果，恢复正常状态
         * 避免动画状态持续影响性能
         */
        setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
      }, 600);
    } else {
      /**
       * 其他tab的加载逻辑（未来活动）
       *
       * 逻辑与历史活动类似，但使用allFutureCourses数据源
       * 代码结构相同，只是数据源和状态变量不同
       */
      const { allFutureCourses } = this.data;
      const lastIdx = allFutureCourses.findIndex(c => c._id === last._id);
      if (lastIdx === -1 || lastIdx === allFutureCourses.length - 1) {
        this.setData({ noMoreFuture: true });
        return;
      }
      this.setData({ isLoadingBottom: true, noMoreFuture: false });
      setTimeout(() => {
        const next = allFutureCourses.slice(lastIdx + 1, lastIdx + 1 + pageSize);
        // 初始化新加载数据的选择状态
        const nextWithSelection = next.map(course => ({
          ...course,
          isSelected: false
        }));
        this.setData({
          visibleCourseList: visibleCourseList.concat(nextWithSelection),
          isLoadingBottom: false,
          flashIndexes: Array.from({length: next.length}, (_, i) => visibleCourseList.length + i),
          noMoreFuture: lastIdx + 1 + pageSize >= allFutureCourses.length
        });
        setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
      }, 600);
    }
  },

  /**
   * 设计说明：移除onCourseScrollToUpper方法
   *
   * 原因：
   * 改用refresher机制处理触顶加载，提供更好的用户体验
   * refresher是小程序官方推荐的下拉刷新解决方案
   *
   * 优势：
   * 1. 原生支持：系统级的下拉刷新动画和交互
   * 2. 性能更好：不需要监听滚动事件
   * 3. 用户体验：符合用户对下拉刷新的操作习惯
   * 4. 兼容性：在不同设备上表现一致
   */

  /**
   * onRefresherRefresh: 下拉刷新事件处理函数
   *
   * 触发时机：
   * 用户下拉页面并松手时触发
   * 需要在WXML中设置refresher-enabled="true"
   *
   * 功能：
   * 加载更多历史数据，实现向上翻页的效果
   * 类似于微信朋友圈的下拉加载更多历史消息
   *
   * 与传统下拉刷新的区别：
   * - 传统：刷新当前页面数据
   * - 这里：加载更多历史数据
   *
   * 异步处理：
   * 使用async/await确保数据加载完成后再结束刷新状态
   */
  async onRefresherRefresh() {
    /**
     * 设置刷新开始状态
     *
     * 状态更新：
     * - isRefresherTriggered: true - 标记刷新已触发
     * - refresherStatus: 'loading' - 设置为加载状态
     *
     * 用途：
     * 1. 控制UI显示相应的loading动画
     * 2. 防止重复触发刷新操作
     * 3. 为其他组件提供状态判断依据
     */
    this.setData({
      isRefresherTriggered: true,
      refresherStatus: 'loading'
    });

    /**
     * 执行历史数据加载
     *
     * await关键字：
     * 等待异步操作完成后再继续执行
     * 确保数据加载完成后再结束刷新状态
     *
     * loadMoreHistoryCourses()：
     * 加载更多历史课程数据的方法
     * 实现时间轴向上翻页的核心逻辑
     */
    await this.loadMoreHistoryCourses();

    /**
     * 设置刷新结束状态
     *
     * 状态重置：
     * - isRefresherTriggered: false - 刷新操作完成
     * - refresherStatus: 'normal' - 恢复正常状态
     *
     * 重要性：
     * 必须重置这些状态，否则刷新动画会一直显示
     * 影响用户体验和后续操作
     */
    this.setData({
      isRefresherTriggered: false,
      refresherStatus: 'normal'
    });
  },

  /**
   * onRefresherPulling: 下拉过程中的状态处理函数
   *
   * 触发时机：
   * 用户正在下拉但尚未松手时持续触发
   * 提供实时的交互反馈
   *
   * 功能：
   * 根据下拉距离更新刷新状态，提供视觉反馈
   * 让用户知道当前的下拉进度和状态
   *
   * 交互设计：
   * 模拟物理世界的弹性效果
   * 下拉距离越大，用户感知的"力度"越大
   */
  onRefresherPulling(e) {
    /**
     * 获取下拉距离
     *
     * e.detail.dy：
     * - dy: delta y，Y轴方向的位移距离
     * - 单位：像素(px)
     * - 正值：向下拉动
     * - 负值：向上推动
     */
    const { dy } = e.detail;

    /**
     * 根据下拉距离设置不同状态
     *
     * 阈值设计：45像素
     * - 小于45px：pulling状态（正在拉动，但未达到刷新条件）
     * - 大于45px：loosing状态（已达到刷新条件，松手即可刷新）
     *
     * 状态含义：
     * - 'pulling': 正在下拉，提示用户继续拉动
     * - 'loosing': 可以松手，提示用户释放即可刷新
     *
     * 用户体验：
     * 通过不同状态提供清晰的操作指引
     * 让用户明确知道何时可以触发刷新
     */
    if (dy > 45) {
      this.setData({ refresherStatus: 'loosing' });
    } else {
      this.setData({ refresherStatus: 'pulling' });
    }
  },

  /**
   * onRefresherRestore: 下拉恢复时的状态处理函数
   *
   * 触发时机：
   * 用户下拉后松手，但没有达到刷新阈值时触发
   * 或者刷新操作被取消时触发
   *
   * 功能：
   * 重置刷新状态为正常状态
   * 确保UI恢复到初始状态
   *
   * 重要性：
   * 保证刷新机制的状态一致性
   * 避免UI状态异常导致的用户体验问题
   */
  onRefresherRestore() {
    /**
     * 重置刷新状态
     *
     * refresherStatus: 'normal'
     * 恢复到正常状态，清除所有刷新相关的UI效果
     */
    this.setData({ refresherStatus: 'normal' });
  },

  /**
   * loadMoreHistoryCourses: 加载更多历史课程数据
   *
   * 功能概述：
   * 实现时间轴向上翻页的核心逻辑，类似于微信朋友圈的历史消息加载
   *
   * 设计理念：
   * 1. 虚拟滚动：只显示部分数据，减少DOM节点
   * 2. 双向分页：支持向前和向后加载数据
   * 3. 智能定位：根据当前位置计算加载范围
   * 4. 边界处理：优雅处理数据边界情况
   *
   * 技术特点：
   * - 复杂的索引计算逻辑
   * - 多种边界条件处理
   * - 动画效果集成
   * - 状态管理完善
   *
   * 与传统分页的区别：
   * - 传统分页：按页码顺序加载
   * - 时间轴分页：按时间位置双向加载
   */
  async loadMoreHistoryCourses() {
    /**
     * 防重复加载检查
     *
     * 如果正在加载顶部数据，直接返回
     * 避免并发加载导致的数据混乱
     */
    if (this.data.isLoadingTop) return;

    /**
     * 获取必要的数据
     *
     * 解构赋值：提取时间轴分页所需的关键数据
     * - visibleCourseList: 当前显示的课程列表
     * - allHistoryCourses: 所有历史课程数据
     * - pageSize: 每次加载的数据量
     */
    const { visibleCourseList, allHistoryCourses, pageSize } = this.data;

    /**
     * 数据有效性检查
     *
     * 边界条件：如果没有历史数据，设置相应状态并返回
     * 防御性编程：避免对空数据进行后续操作
     */
    if (!allHistoryCourses || allHistoryCourses.length === 0) {
      this.setData({ noMoreHistory: true });
      return;
    }

    /**
     * 定位当前显示的第一个课程
     *
     * 逻辑：
     * 1. 获取当前可见列表的第一项
     * 2. 在完整历史数据中查找其位置
     * 3. 根据位置计算向前加载的范围
     */
    const first = visibleCourseList[0];
    const firstIdx = allHistoryCourses.findIndex(c => c._id === first._id);
    let startIdx = 0;

    /**
     * 计算加载起始位置
     *
     * 三种情况的处理策略：
     */
    if (firstIdx === -1) {
      /**
       * 情况1：当前第一项不在历史数据中
       *
       * 原因：可能是未来数据或数据不一致
       * 策略：从历史数据的末尾开始加载
       *
       * Math.max(0, value)：
       * 确保索引不会小于0，避免数组越界
       */
      startIdx = Math.max(0, allHistoryCourses.length - pageSize);
    } else if (firstIdx === 0) {
      /**
       * 情况2：已经到达历史数据的开头
       *
       * 说明：没有更多历史数据可以加载
       * 策略：设置状态并提前返回
       */
      this.setData({ noMoreHistory: true });
      return;
    } else {
      /**
       * 情况3：正常情况，从当前位置向前加载
       *
       * 计算：当前位置减去页面大小，得到向前加载的起始位置
       * Math.max(0, value)：确保不会超出数组边界
       */
      startIdx = Math.max(0, firstIdx - pageSize);
    }

    /**
     * 计算加载结束位置
     *
     * 三元运算符：根据firstIdx是否有效选择不同的结束位置
     * - firstIdx === -1: 使用完整数据长度
     * - firstIdx !== -1: 使用当前位置作为结束位置
     */
    const endIdx = firstIdx === -1 ? allHistoryCourses.length : firstIdx;

    /**
     * 边界检查：如果结束位置无效，设置状态并返回
     */
    if (endIdx <= 0) {
      this.setData({ noMoreHistory: true });
      return;
    }

    /**
     * 设置加载状态
     *
     * 状态更新：
     * - isLoadingTop: true - 开始顶部加载
     * - noMoreHistory: false - 重置"没有更多"状态
     */
    this.setData({ isLoadingTop: true, noMoreHistory: false });

    /**
     * 延迟加载处理
     *
     * 600ms延迟的作用：
     * 1. 提供视觉反馈时间
     * 2. 模拟真实的网络请求延迟
     * 3. 避免加载过快导致的闪烁
     * 4. 给用户足够的心理准备时间
     */
    setTimeout(() => {
      /**
       * 数据切片提取
       *
       * slice(startIdx, endIdx)：
       * 提取从startIdx到endIdx（不包含）的数据
       * 这些数据将被插入到当前显示列表的前面
       */
      const prev = allHistoryCourses.slice(startIdx, endIdx);

      /**
       * 更新显示数据和状态
       */
      this.setData({
        /**
         * 数据合并：将新数据插入到现有数据前面
         *
         * prev.concat(visibleCourseList)：
         * 新加载的历史数据 + 当前显示的数据
         * 实现向上翻页的效果
         */
        visibleCourseList: prev.concat(visibleCourseList),

        // 结束顶部加载状态
        isLoadingTop: false,

        /**
         * 设置动画索引
         *
         * Array.from({length: prev.length}, (_, i) => i)：
         * 为新加载的数据生成从0开始的索引数组
         * 用于触发滑入动画效果
         */
        flashIndexes: Array.from({length: prev.length}, (_, i) => i),

        /**
         * 更新"没有更多"状态
         *
         * startIdx === 0：
         * 如果起始位置是0，说明已经加载到历史数据的开头
         * 没有更多历史数据可以加载了
         */
        noMoreHistory: startIdx === 0
      });

      /**
       * 清除动画效果
       *
       * 1000ms后清空动画索引
       * 避免动画状态持续影响性能
       */
      setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
    }, 600);
  },

  /**
   * loadBookedStudents: 获取已预约学员信息
   *
   * 功能概述：
   * 为课程列表中的每个课程加载对应的预约学员信息
   * 包括预约人数统计和学员详细信息
   *
   * 设计模式：
   * 采用纯函数设计，不直接修改页面状态
   * 返回增强后的新数组，由调用方决定如何使用
   *
   * 性能考虑：
   * 使用for循环而非Promise.all，避免并发请求过多
   * 对数据库查询进行了多层降级处理
   *
   * 数据兼容性：
   * 处理不同版本数据结构的兼容性问题
   * 支持多种用户ID字段格式
   *
   * @param {Array} courseList - 课程列表数组
   * @returns {Array} 增强后的课程列表，包含预约信息
   *
   * 与您熟悉的技术对比：
   * - 类似于C#的LINQ Select操作
   * - 类似于Java的Stream.map()转换
   * - 数据库查询类似于Entity Framework的Include操作
   */
  async loadBookedStudents(courseList) {
    /**
     * 异常处理包装
     *
     * try-catch模式：
     * 确保即使数据库操作失败，也不会影响页面正常显示
     * 失败时返回原始数据，保证功能降级
     */
    try {
      /**
       * 数据库连接初始化
       *
       * wx.cloud.database()：
       * 获取云数据库实例，用于后续的数据查询操作
       * 类似于传统开发中的数据库连接对象
       */
      const db = wx.cloud.database();

      /**
       * 结果数组初始化
       *
       * 用于存储增强后的课程数据
       * 每个课程都会添加预约相关的字段
       */
      const newList = [];

      /**
       * 遍历课程列表
       *
       * for...of循环：
       * 顺序处理每个课程，避免并发查询过多导致的性能问题
       * 相比于Promise.all，这种方式更稳定但速度稍慢
       *
       * 设计权衡：
       * - 稳定性 > 速度
       * - 避免数据库连接池耗尽
       * - 减少服务器压力
       */
      for (let course of courseList) {
        /**
         * 课程ID兼容性处理
         *
         * 逻辑或运算符：course._id || course.id
         * 处理不同数据源的ID字段差异
         * - _id: 云数据库的标准ID字段
         * - id: 可能的自定义ID字段
         */
        const cid = course._id || course.id;

        /**
         * 查询课程预约记录
         *
         * 查询条件：
         * - courseId: 课程ID，关联到具体课程
         * - status: 'upcoming'，只查询有效的预约记录
         *
         * 数据库查询语法：
         * .where({条件对象}): 设置查询条件
         * .get(): 执行查询并获取结果
         *
         * 类似SQL：
         * SELECT * FROM bookings WHERE courseId = ? AND status = 'upcoming'
         */
        const bookings = await db.collection('bookings')
          .where({ courseId: cid, status: 'upcoming' })
          .get();

        /**
         * 降级查询处理
         *
         * 业务场景：
         * 如果没有找到'upcoming'状态的预约，可能是状态字段不一致
         * 或者历史数据没有status字段，需要查询所有预约记录
         *
         * 降级策略：
         * 1. 优先查询有效预约
         * 2. 如果没有，查询所有预约记录
         * 3. 确保数据完整性
         */
        if (!bookings.data || bookings.data.length === 0) {
          const allBookings = await db.collection('bookings')
            .where({ courseId: cid })
            .get();
          if (allBookings.data && allBookings.data.length > 0) {
            bookings.data = allBookings.data;
          }
        }

        /**
         * 预约人数统计
         *
         * 防御性编程：
         * 使用三元运算符确保即使data为null也能得到有效数值
         * 避免后续计算出现NaN或undefined
         */
        let bookedCount = bookings.data ? bookings.data.length : 0;

        /**
         * 学员信息初始化
         *
         * 用于存储预约学员的详细信息
         * 包括姓名、头像、联系方式等
         */
        let bookedStudents = [];

        /**
         * 学员信息查询处理
         *
         * 只有当存在预约记录时才查询学员信息
         * 避免不必要的数据库查询
         */
        if (bookings.data && bookings.data.length > 0) {
          /**
           * 用户ID提取和兼容性处理
           *
           * 多字段兼容：
           * 不同版本的数据可能使用不同的字段名存储用户ID
           * 需要按优先级尝试不同的字段
           *
           * 字段优先级：
           * 1. userId: 标准用户ID字段
           * 2. user_id: 下划线命名风格
           * 3. userOpenid: 微信openid字段
           */
          let userIds = bookings.data.map(booking => booking.userId);
          if (!userIds[0]) userIds = bookings.data.map(booking => booking.user_id);
          if (!userIds[0]) userIds = bookings.data.map(booking => booking.userOpenid);

          /**
           * 用户信息查询
           *
           * db.command.in(userIds)：
           * 数据库的IN查询操作，类似于SQL的WHERE id IN (1,2,3)
           * 一次查询获取多个用户的信息，提高效率
           *
           * 查询字段：openid
           * 微信小程序中用户的唯一标识符
           */
          const users = await db.collection('users')
            .where({ openid: db.command.in(userIds) })
            .get();

          /**
           * 用户查询降级处理
           *
           * 兼容性考虑：
           * 如果通过openid字段查询不到用户，尝试使用_openid字段
           * 处理不同版本数据结构的差异
           */
          if (!users.data || users.data.length === 0) {
            const usersByOpenid = await db.collection('users')
              .where({ _openid: db.command.in(userIds) })
              .get();
            if (usersByOpenid.data && usersByOpenid.data.length > 0) {
              bookedStudents = usersByOpenid.data;
            }
          } else {
            bookedStudents = users.data;
          }

          /**
           * 为学员数据添加预约ID信息
           *
           * 功能：为每个学员添加对应的预约记录ID
           * 用途：管理员移除学员预约时需要使用预约ID
           */
          bookedStudents = bookedStudents.map(student => {
            // 查找该学员对应的预约记录
            const booking = bookings.data.find(b =>
              b.userId === student.openid ||
              b.user_id === student.openid ||
              b.userOpenid === student.openid ||
              b.userId === student._openid ||
              b.user_id === student._openid ||
              b.userOpenid === student._openid
            );



            // 返回增强后的学员数据
            return {
              ...student,
              bookingId: booking ? booking._id : null // 添加预约ID
            };
          });
        }

        /**
         * 构建增强后的课程对象
         *
         * 扩展运算符：...course
         * 保留原课程对象的所有属性
         *
         * 新增字段：
         * - bookedCount: 预约人数统计
         * - bookedStudents: 预约学员详细信息数组
         * - collapseValue: 折叠面板状态（保持现有状态或初始化为空）
         */
        newList.push({
          ...course,                                    // 保留原有属性
          bookedCount,                                  // 添加预约人数
          bookedStudents,                               // 添加学员信息
          collapseValue: course.collapseValue || []     // 保持或初始化折叠状态
        });
      }

      /**
       * 返回增强后的数据
       *
       * 纯函数设计：
       * 不修改输入参数，返回新的数组
       * 便于测试和调试，避免副作用
       */
      return newList;
    } catch (error) {
      /**
       * 错误处理和降级
       *
       * 错误记录：
       * 使用console.error记录详细错误信息
       * 便于开发者调试和问题排查
       *
       * 优雅降级：
       * 返回原始课程列表，确保页面功能不受影响
       * 用户可能看不到预约信息，但基本功能仍可使用
       */
      console.error('获取已预约学员信息失败:', error);
      return courseList;
    }
  },

  /**
   * loadTemplateList: 加载课程模板列表
   *
   * 功能概述：
   * 从云数据库获取所有课程模板，并按应用状态进行分类
   * 为模板管理功能提供数据支持
   *
   * 业务价值：
   * 1. 模板复用：快速创建相似的课程
   * 2. 标准化：统一课程信息格式
   * 3. 效率提升：减少重复输入工作
   * 4. 管理便利：集中管理常用课程配置
   *
   * 数据处理流程：
   * 1. 从数据库获取原始模板数据
   * 2. 格式化模板数据，添加显示字段
   * 3. 按应用状态分类存储
   * 4. 更新当前显示列表
   *
   * 与您熟悉的技术对比：
   * - 类似于C#的Repository模式数据访问
   * - 类似于Java的DAO层数据操作
   * - 数据分类类似于SQL的GROUP BY操作
   */
  async loadTemplateList() {
    /**
     * 设置加载状态
     *
     * 用户体验：
     * 显示loading状态，告知用户数据正在加载
     * 避免用户在等待过程中进行其他操作
     */
    this.setData({ loading: true });

    /**
     * 异常处理包装
     *
     * try-catch-finally结构：
     * - try: 执行主要的数据加载逻辑
     * - catch: 处理可能的错误情况
     * - finally: 无论成功失败都要清除loading状态
     */
    try {
      /**
       * 数据库连接和查询
       *
       * 云数据库操作：
       * - wx.cloud.database(): 获取数据库实例
       * - collection('coursesTemplate'): 选择模板集合
       * - get(): 获取所有模板数据
       *
       * 类似SQL：SELECT * FROM coursesTemplate
       */
      const db = wx.cloud.database();
      const res = await db.collection('coursesTemplate').get();
      const templates = res.data || [];

      /**
       * 模板数据格式化函数
       *
       * 设计模式：函数式编程
       * 将数据转换逻辑封装为纯函数，便于测试和复用
       *
       * 功能：为每个模板添加显示所需的字段
       */
      const formatTemplateList = (templates) => {
        /**
         * Array.map()转换：
         * 遍历每个模板，返回增强后的模板对象
         *
         * 扩展运算符：...template
         * 保留原模板的所有属性
         */
        return templates.map(template => ({
          ...template,                    // 保留原有属性

          /**
           * id字段标准化
           *
           * 目的：统一ID字段名称
           * 云数据库使用_id，但前端习惯使用id
           * 提供两种字段便于不同场景使用
           */
          id: template._id,

          /**
           * 教练信息显示处理
           *
           * 业务逻辑：
           * - 如果coach是数组：显示教练数量
           * - 如果coach不是数组：显示0
           *
           * Array.isArray()检查：
           * 判断数据类型，确保后续操作的安全性
           *
           * 应用场景：
           * 在列表中显示"3位教练"而不是具体的教练名单
           * 节省显示空间，提供概览信息
           */
          coachDisplay: Array.isArray(template.coach) ? template.coach.length : 0
        }));
      };

      /**
       * 模板分类存储
       *
       * 业务规则：
       * 根据applied字段将模板分为三类：
       * 1. 全部模板：所有模板数据
       * 2. 已应用模板：applied === true的模板
       * 3. 未应用模板：applied !== true的模板（包括undefined）
       *
       * 数据结构设计：
       * 分别存储不同类别的模板，支持快速切换显示
       * 避免每次切换tab都重新过滤数据
       */
      this.setData({
        /**
         * 全部模板列表
         *
         * 包含所有模板，不进行任何过滤
         * 作为其他分类的数据源
         */
        allTemplateList: formatTemplateList(templates),

        /**
         * 已应用模板列表
         *
         * filter()方法：筛选applied为true的模板
         * 这些模板已经被用于创建实际课程
         */
        appliedTemplateList: formatTemplateList(templates.filter(t => t.applied === true)),

        /**
         * 未应用模板列表
         *
         * filter()方法：筛选applied不为true的模板
         * 包括applied为false或undefined的情况
         * 这些模板尚未被使用，可以考虑删除或应用
         */
        unappliedTemplateList: formatTemplateList(templates.filter(t => t.applied !== true))
      });

      /**
       * 更新显示列表
       *
       * 根据当前选中的tab更新实际显示的模板列表
       * 确保数据加载完成后立即显示正确的内容
       */
      this.updateDisplayTemplateList();

      /**
       * 更新可见模板列表
       *
       * 根据当前筛选状态和搜索条件更新可见列表
       */
      this.updateVisibleTemplateList();

    } catch (error) {
      /**
       * 错误处理
       *
       * 错误记录：
       * console.error记录详细错误信息，便于调试
       *
       * 用户提示：
       * showMessage显示用户友好的错误信息
       * 不暴露技术细节，保持良好的用户体验
       */
      console.error('加载模板列表失败:', error);
      this.showMessage('加载模板列表失败', 'error');
    } finally {
      /**
       * 清理工作
       *
       * 无论成功失败都要清除loading状态
       * 确保UI不会一直显示加载动画
       * 恢复用户的正常操作能力
       */
      this.setData({ loading: false });
    }
  },

  /**
   * updateDisplayTemplateList: 更新显示的模板列表
   *
   * 功能：设置模板列表数据
   * 修改：移除分类功能，直接使用全部模板列表
   *
   * 调用时机：
   * 1. 模板数据加载完成后
   * 2. 模板数据发生变化后
   */
  updateDisplayTemplateList() {
    /**
     * 获取全部模板列表
     *
     * 不再需要分类，直接使用全部模板
     */
    const { allTemplateList } = this.data;

    /**
     * 更新显示数据
     *
     * 设置templateList为全部模板列表
     * 触发页面重新渲染，显示模板数据
     */
    this.setData({ templateList: allTemplateList || [] });
  },

  /**
   * onTopTabsChange: 顶部选项卡切换事件处理函数
   *
   * 功能：
   * 处理"活动维护"和"模板维护"之间的切换
   * 实现不同功能模块的数据和UI切换
   *
   * 触发时机：
   * 用户点击顶部的tab选项卡时触发
   *
   * 事件参数：
   * e.detail.value: TDesign组件传递的选中值
   *
   * 业务逻辑：
   * 1. 更新当前选中的顶部tab
   * 2. 如果切换到课程tab，刷新课程数据
   * 3. 确保数据的实时性和准确性
   *
   * 异步处理：
   * 使用async/await确保数据加载完成后再进行后续操作
   */
  async onTopTabsChange(e) {
    /**
     * 获取选中的tab值
     *
     * e.detail.value: TDesign组件的标准事件参数格式
     * 包含用户选中的tab的value值
     */
    const value = e.detail.value;

    /**
     * 切换条件检查
     *
     * 两个条件：
     * 1. value存在且有效
     * 2. value与当前选中的tab不同
     *
     * 目的：
     * - 避免重复切换到相同tab
     * - 减少不必要的数据加载
     * - 提升性能和用户体验
     */
    if (value && value !== this.data.topActiveTab) {
      /**
       * 更新顶部tab状态
       *
       * 立即更新UI状态，给用户即时的视觉反馈
       * 即使后续数据加载较慢，用户也能看到tab已切换
       */
      this.setData({ topActiveTab: value });

      /**
       * 不同tab的特殊处理
       *
       * 业务需求：
       * 切换到不同tab时，需要刷新对应的数据
       * 确保显示最新的信息
       */
      if (value === 'course') {
        /**
         * 课程tab处理
         *
         * reset=true参数：
         * 清空现有数据，从第一页重新加载
         * 确保数据的完整性和一致性
         */
        await this.loadCourseListPaged(true);
      } else if (value === 'template') {
        /**
         * 模板tab处理
         *
         * 刷新模板数据并更新可见列表
         * 确保模板信息是最新的
         */
        await this.loadTemplateList();
        this.updateVisibleTemplateList();
      }
    }
  },

  /**
   * onCourseTabsChange: 课程筛选选项卡切换事件处理函数
   *
   * 功能：
   * 处理课程状态筛选tab的切换（全部、已上线、未上线、历史）
   * 实现不同状态课程的快速切换和缓存机制
   *
   * 设计特点：
   * 1. 兼容多种事件源：支持TDesign组件和原生view组件
   * 2. 智能缓存：优先使用缓存数据，提升响应速度
   * 3. 降级加载：缓存无效时自动加载新数据
   * 4. 状态同步：确保分页状态和显示数据的一致性
   *
   * 缓存策略：
   * 为每个tab维护独立的数据缓存和分页状态
   * 类似于浏览器的前进后退缓存机制
   */
  onCourseTabsChange(e) {
    /**
     * 事件参数兼容性处理
     *
     * 支持两种事件源：
     * 1. e.currentTarget.dataset.value: 原生view组件的自定义数据属性
     * 2. e.detail.value: TDesign组件的标准事件参数
     *
     * 逻辑或运算符：优先使用第一种，如果不存在则使用第二种
     *
     * 设计原因：
     * 页面中可能同时使用不同类型的组件实现tab功能
     * 需要统一的事件处理逻辑
     */
    const value = e.currentTarget ? e.currentTarget.dataset.value : (e.detail && e.detail.value);

    /**
     * 切换条件检查
     *
     * 避免重复切换到相同的课程tab
     * 减少不必要的数据操作和UI更新
     */
    if (value && value !== this.data.courseActiveTab) {
      /**
       * 更新课程tab状态
       *
       * setData的回调机制：
       * 第二个参数是回调函数，在状态更新完成后执行
       * 确保后续操作基于最新的状态数据
       */
      this.setData({ courseActiveTab: value }, () => {
        /**
         * 缓存优先策略
         *
         * 缓存机制：
         * 1. 检查目标tab是否有有效缓存
         * 2. 如果有缓存且数据不为空，优先使用缓存
         * 3. 如果缓存无效，触发新的数据加载
         *
         * 缓存结构：
         * courseTabCache[tabValue] = {
         *   list: Array,     // 该tab的课程数据
         *   page: number,    // 该tab的当前页码
         *   hasMore: boolean // 该tab是否还有更多数据
         * }
         */
        const cache = this.data.courseTabCache[value];

        /**
         * 缓存有效性检查
         *
         * 条件：
         * 1. cache存在：该tab之前被访问过
         * 2. cache.list.length > 0：缓存中有实际数据
         *
         * 缓存优势：
         * - 即时响应：无需等待网络请求
         * - 减少流量：避免重复下载相同数据
         * - 保持状态：记住滚动位置和加载进度
         */
        if (cache && cache.list.length > 0) {
          /**
           * 使用缓存数据
           *
           * 恢复该tab的完整状态：
           * - courseList: 课程数据列表
           * - coursePage: 分页页码
           * - courseHasMore: 是否还有更多数据
           *
           * 嵌套回调：
           * 确保缓存数据设置完成后再执行过滤操作
           */
          this.setData({
            courseList: cache.list,
            coursePage: cache.page,
            courseHasMore: cache.hasMore
          }, () => {
            /**
             * 重要：缓存数据的过滤处理
             *
             * 为什么需要调用_filterAndSetCourses()：
             * 1. 缓存的是原始数据，可能需要应用搜索过滤
             * 2. 确保filteredCourseList与当前搜索条件一致
             * 3. 保持搜索功能在tab切换后仍然有效
             *
             * 函数命名：
             * 下划线前缀表示这是内部私有方法
             * 不应该被外部直接调用
             */
            this._filterAndSetCourses();
          });
        } else {
          /**
           * 缓存降级处理
           *
           * 当缓存无效或不存在时的处理策略：
           * 1. 缓存为空：该tab首次访问
           * 2. 缓存过期：数据可能已经过时
           * 3. 缓存损坏：数据结构异常
           *
           * 降级方案：
           * 调用loadCourseListPaged(true)重新加载数据
           * reset=true确保清空现有数据，从第一页开始
           *
           * 用户体验：
           * 虽然需要等待网络请求，但确保数据的准确性
           * 避免显示过期或错误的数据
           */
          this.loadCourseListPaged(true);
        }
      });
    }
  },



  /**
   * onAddCourse: 添加活动按钮点击事件处理函数
   *
   * 功能：跳转到课程编辑页面，创建新的课程
   *
   * 页面导航：
   * wx.navigateTo(): 小程序的页面跳转API
   * - 保留当前页面，跳转到新页面
   * - 用户可以通过返回按钮回到当前页面
   * - 类似于浏览器的pushState操作
   *
   * URL参数：
   * 不传递任何参数，表示创建新课程
   * 编辑页面会根据参数的有无判断是创建还是编辑模式
   */
  onAddCourse() {
    wx.navigateTo({ url: '/pages/course-edit/course-edit' });
  },

  /**
   * onAddTemplate: 添加模板按钮点击事件处理函数
   *
   * 功能：跳转到课程编辑页面，创建新的课程模板
   *
   * URL参数：
   * mode=template: 告知编辑页面当前是模板创建模式
   * 编辑页面会根据此参数调整UI和保存逻辑
   *
   * 模板与课程的区别：
   * - 模板：可复用的课程配置，不包含具体时间
   * - 课程：具体的活动实例，包含开始时间、结束时间等
   */
  onAddTemplate() {
    wx.navigateTo({ url: '/pages/course-edit/course-edit?mode=template' });
  },



  /**
   * 模板批量模式切换事件处理函数
   *
   * 功能：开启/关闭模板批量操作模式
   * 照搬活动维护的批量模式切换逻辑
   */
  onToggleTemplateBatchMode() {
    const newBatchMode = !this.data.templateBatchMode;
    console.log('切换模板批量模式:', newBatchMode);

    // 更新模板列表，清空所有选择状态
    const updatedTemplateList = this.data.visibleTemplateList.map(template => ({
      ...template,
      isSelected: false
    }));

    this.setData({
      templateBatchMode: newBatchMode,
      selectedTemplateIds: [], // 切换模式时清空选择
      visibleTemplateList: updatedTemplateList
    }, () => {
      console.log('模板批量模式状态更新完成:', this.data.templateBatchMode);
    });
  },

  /**
   * 模板选择事件处理函数
   *
   * 功能：处理模板卡片的选择/取消选择
   * 照搬活动维护的选择逻辑，保持一致性
   */
  onTemplateSelect(e) {
    const templateId = e.currentTarget.dataset.templateId;
    if (!templateId) {
      console.error('模板ID不存在');
      return;
    }

    this._toggleTemplateSelection(templateId);
  },

  /**
   * 切换模板选择状态
   *
   * 功能：切换指定模板的选择状态
   * 参数：templateId - 模板ID
   * 照搬活动维护的选择逻辑
   */
  _toggleTemplateSelection(templateId) {
    const { selectedTemplateIds, visibleTemplateList } = this.data;
    const isSelected = selectedTemplateIds.includes(templateId);

    let newSelectedIds;
    if (isSelected) {
      // 取消选择
      newSelectedIds = selectedTemplateIds.filter(id => id !== templateId);
    } else {
      // 选择
      newSelectedIds = [...selectedTemplateIds, templateId];
    }

    // 更新模板列表中的选择状态
    const updatedTemplateList = visibleTemplateList.map(template => ({
      ...template,
      isSelected: newSelectedIds.includes(template._id)
    }));

    this.setData({
      selectedTemplateIds: newSelectedIds,
      visibleTemplateList: updatedTemplateList
    });

    console.log('模板选择状态更新:', { templateId, isSelected: !isSelected, selectedCount: newSelectedIds.length });
  },

  /**
   * 模板卡片点击事件处理函数
   *
   * 功能：处理非批量模式下的模板卡片点击
   * 可以用于跳转到模板详情页面等操作
   */
  onTemplateCardTap(e) {
    if (this.data.templateBatchMode) {
      // 批量模式下不处理卡片点击
      return;
    }

    const template = e.currentTarget.dataset.template;
    console.log('模板卡片点击:', template);

    // 这里可以添加跳转到模板详情页面的逻辑
    // 暂时不实现，保持与原有逻辑一致
  },

  /**
   * 批量删除模板事件处理函数
   *
   * 功能：删除选中的模板，需要5秒确认
   * 照搬活动维护的批量删除逻辑
   */
  onBatchDeleteTemplates() {
    const selectedIds = this.data.selectedTemplateIds;
    if (selectedIds.length === 0) {
      showToast(this, {
        message: '请先选择要删除的模板',
        theme: 'warning',
        duration: 2000
      });
      return;
    }

    console.log('批量删除模板:', selectedIds);

    // 使用与活动维护相同的确认机制
    this._showTemplateBatchConfirm('delete', '批量删除');
  },

  /**
   * 显示模板批量操作确认对话框
   *
   * 功能：显示带5秒倒计时的确认对话框
   * 照搬活动维护的确认对话框逻辑
   */
  _showTemplateBatchConfirm(operationType, operationName) {
    const count = this.data.selectedTemplateIds.length;
    const content = `确定要${operationName} ${count} 个模板吗？此操作不可撤销。`;

    this.setData({
      templateBatchOperationType: operationType,
      templateBatchConfirmDialogVisible: true,
      templateBatchConfirmContent: content,
      templateBatchCountdown: 5,
      templateBatchConfirmBtn: `确认${operationName}(5)`
    });

    // 开始倒计时
    this._startTemplateBatchCountdown();
  },

  /**
   * 开始模板批量操作倒计时
   *
   * 功能：5秒倒计时，防止误操作
   * 照搬活动维护的倒计时逻辑
   */
  _startTemplateBatchCountdown() {
    const timer = setInterval(() => {
      const countdown = this.data.templateBatchCountdown - 1;
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          templateBatchCountdown: 0,
          templateBatchConfirmBtn: `确认${this._getTemplateOperationName()}`
        });
      } else {
        this.setData({
          templateBatchCountdown: countdown,
          templateBatchConfirmBtn: `确认${this._getTemplateOperationName()}(${countdown})`
        });
      }
    }, 1000);
  },

  /**
   * 获取模板操作名称
   *
   * 功能：根据操作类型返回对应的操作名称
   * 照搬活动维护的操作名称逻辑
   */
  _getTemplateOperationName() {
    const { templateBatchOperationType } = this.data;
    switch (templateBatchOperationType) {
      case 'delete': return '批量删除';
      default: return '批量操作';
    }
  },

  /**
   * 模板批量操作确认
   *
   * 功能：确认执行模板批量操作
   * 照搬活动维护的确认逻辑
   */
  onTemplateBatchConfirm() {
    if (this.data.templateBatchCountdown > 0) {
      showToast(this, {
        message: '请等待倒计时结束',
        theme: 'warning',
        duration: 2000
      });
      return;
    }

    this._executeTemplateBatchOperation();
  },

  /**
   * 模板批量操作取消
   *
   * 功能：取消模板批量操作
   * 照搬活动维护的取消逻辑
   */
  onTemplateBatchCancel() {
    this.setData({
      templateBatchConfirmDialogVisible: false,
      templateBatchOperationType: '',
      templateBatchCountdown: 5
    });
  },

  /**
   * 执行模板批量操作
   *
   * 功能：实际执行模板批量操作
   * 照搬活动维护的执行逻辑
   */
  async _executeTemplateBatchOperation() {
    const { templateBatchOperationType, selectedTemplateIds } = this.data;

    if (selectedTemplateIds.length === 0) {
      showToast(this, {
        message: '请选择要操作的模板',
        theme: 'warning',
        duration: 2000
      });
      return;
    }

    // 关闭确认对话框
    this.setData({ templateBatchConfirmDialogVisible: false });

    showLoading(this, '批量操作中...');

    try {
      if (templateBatchOperationType === 'delete') {
        // 执行批量删除
        await this.deleteTemplatesById(selectedTemplateIds);

        showToast(this, {
          message: `成功删除 ${selectedTemplateIds.length} 个模板`,
          theme: 'success',
          duration: 2000
        });

        // 刷新数据
        await this.loadTemplateList();

        // 退出批量模式
        this.setData({
          templateBatchMode: false,
          selectedTemplateIds: [],
          templateBatchOperationType: ''
        });
      }
    } catch (error) {
      console.error('模板批量操作失败:', error);
      showToast(this, {
        message: '批量操作失败',
        theme: 'error',
        duration: 2000
      });
    } finally {
      hideToast(this);
    }
  },

  /**
   * 展开模板搜索框事件处理函数
   *
   * 功能：展开模板搜索输入框
   */
  onExpandTemplateSearch() {
    console.log('展开模板搜索框');
    this.setData({
      templateSearchExpanded: true
    });
  },

  /**
   * 收起模板搜索框事件处理函数
   *
   * 功能：收起模板搜索输入框
   */
  onCollapseTemplateSearch() {
    console.log('收起模板搜索框');
    this.setData({
      templateSearchExpanded: false,
      templateSearchKeyword: '' // 收起时清空搜索
    });

    // 重新加载模板数据
    this.loadTemplateList().then(() => {
      this.updateVisibleTemplateList();
    });
  },

  /**
   * 模板搜索输入事件处理函数
   *
   * 功能：处理模板搜索关键词输入
   * 参数：event.detail.value - 输入的搜索关键词
   */
  onTemplateSearchInput(event) {
    const keyword = event.detail.value;
    console.log('模板搜索输入:', keyword);

    this.setData({
      templateSearchKeyword: keyword
    });

    // 实时搜索
    this.searchTemplates();
  },

  /**
   * 模板搜索提交事件处理函数
   *
   * 功能：处理模板搜索确认
   */
  onTemplateSearchSubmit() {
    console.log('模板搜索提交:', this.data.templateSearchKeyword);
    this.searchTemplates();
  },

  /**
   * 模板搜索失焦事件处理函数
   *
   * 功能：处理模板搜索输入框失焦
   */
  onTemplateSearchBlur() {
    console.log('模板搜索失焦');
    // 可以在这里添加失焦时的处理逻辑
  },

  /**
   * 清空模板搜索事件处理函数
   *
   * 功能：清空模板搜索关键词
   */
  onTemplateSearchClear() {
    console.log('清空模板搜索');
    this.setData({
      templateSearchKeyword: ''
    });

    // 重新加载模板数据
    this.loadTemplateList().then(() => {
      this.updateVisibleTemplateList();
    });
  },

  /**
   * 模板搜索方法
   *
   * 功能：根据关键词搜索模板
   */
  searchTemplates() {
    console.log('搜索模板:', this.data.templateSearchKeyword);

    // 直接使用统一的更新方法
    this.updateVisibleTemplateList();
  },

  /**
   * 模板滚动到底部事件处理函数
   *
   * 功能：处理模板列表滚动到底部，加载更多数据
   */
  onTemplateScrollToLower() {
    console.log('模板列表滚动到底部');
    // 如果需要分页加载，可以在这里实现
  },

  /**
   * onBackToToday: 回到今天按钮点击事件处理函数
   *
   * 功能：在当前选项卡中滚动到今天的活动位置
   *
   * 实现逻辑：
   * 1. 保持当前选项卡状态不变
   * 2. 清空搜索关键词，显示完整列表
   * 3. 重新加载当前选项卡的数据
   * 4. 计算今天的日期格式，滚动到今天的时间轴位置
   *
   * 用户体验：
   * - 保持用户当前的筛选状态
   * - 在当前选项卡中精确定位到今天
   * - 如果当前选项卡没有今天的数据，给出相应提示
   */
  onBackToToday() {
    console.log('点击回到今天按钮，当前选项卡:', this.data.courseActiveTab);

    // 只清空搜索条件，保持当前选项卡状态
    this.setData({
      searchKeyword: '', // 清空搜索关键词
      isSearchExpanded: false, // 收起搜索框
      scrollIntoView: '' // 先清空滚动目标
    });

    // 重新加载当前选项卡的数据
    this.loadCourseListPaged(true).then(() => {
      // 数据加载完成后，计算今天的日期并滚动到对应位置
      this.scrollToToday();
    });
  },

  /**
   * scrollToToday: 滚动到今天的时间轴位置
   *
   * 功能：计算今天的日期格式，并滚动到对应的时间轴位置
   */
  scrollToToday() {
    // 计算今天的日期格式（与时间轴格式保持一致）
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const day = today.getDate();
    const weekMap = ['日', '一', '二', '三', '四', '五', '六'];
    const week = weekMap[today.getDay()];
    const todayTimelineDate = `${year}年${month}月${day}日（周${week}）`;

    console.log('今天的时间轴日期:', todayTimelineDate);
    console.log('当前选项卡:', this.data.courseActiveTab);

    // 检查今天的日期是否在可见列表中
    const visibleCourseList = this.data.visibleCourseList || [];
    const todayCourse = visibleCourseList.find(course => course.timelineDate === todayTimelineDate);

    if (todayCourse) {
      // 如果找到今天的课程，滚动到对应位置
      // 对日期字符串进行编码，避免特殊字符导致的问题
      const encodedDate = encodeURIComponent(todayTimelineDate);
      const scrollTarget = `timeline-${encodedDate}`;
      console.log('滚动到:', scrollTarget);

      this.setData({
        scrollIntoView: scrollTarget
      });

      // 显示成功提示
      showToast(this, {
        message: '已定位到今天',
        theme: 'success',
        duration: 1500
      });

      // 清空滚动目标，避免影响后续滚动
      setTimeout(() => {
        this.setData({
          scrollIntoView: ''
        });
      }, 1000);
    } else {
      // 根据当前选项卡给出不同的提示
      const currentTab = this.data.courseActiveTab;
      let message = '';

      if (currentTab === 'online') {
        message = '今天没有已上线的活动';
      } else if (currentTab === 'offline') {
        message = '今天没有未上线的活动';
      } else if (currentTab === 'history') {
        message = '历史活动中无今天数据';
      } else {
        message = '今天没有活动';
      }

      console.log('今天没有课程，滚动到顶部，提示:', message);

      // 滚动到顶部
      this.setData({
        scrollIntoView: 'top'
      });

      // 显示相应提示
      showToast(this, {
        message: message,
        theme: 'warning',
        duration: 2000
      });

      // 清空滚动目标
      setTimeout(() => {
        this.setData({
          scrollIntoView: ''
        });
      }, 1000);
    }
  },

  /**
   * onEditCourse: 编辑活动按钮点击事件处理函数
   *
   * 功能：跳转到课程编辑页面，编辑现有课程
   *
   * 事件参数获取：
   * e.currentTarget.dataset.id: 从DOM元素的data-id属性获取课程ID
   * 这是小程序中传递数据的标准方式
   *
   * URL参数：
   * id=${courseId}: 传递要编辑的课程ID
   * 编辑页面会根据此ID加载对应的课程数据
   *
   * 模板字符串：
   * 使用ES6模板字符串语法构建动态URL
   * 比字符串拼接更清晰、更不容易出错
   */
  onEditCourse(e) {
    /**
     * 获取课程ID
     *
     * dataset: 小程序中访问自定义数据属性的标准方式
     * data-id="123" 对应 dataset.id
     */
    const courseId = e.currentTarget.dataset.id;

    /**
     * 页面跳转
     *
     * 模板字符串：`/pages/course-edit/course-edit?id=${courseId}`
     * 动态构建包含课程ID的URL
     */
    wx.navigateTo({ url: `/pages/course-edit/course-edit?id=${courseId}` });
  },

  /**
   * onEditTemplate: 编辑模板按钮点击事件处理函数
   *
   * 功能：跳转到课程编辑页面，编辑现有模板
   *
   * URL参数组合：
   * - mode=template: 指定为模板编辑模式
   * - id=${templateId}: 指定要编辑的模板ID
   *
   * 参数顺序：
   * mode参数在前，id参数在后
   * 便于编辑页面优先判断模式，再加载对应数据
   */
  onEditTemplate(e) {
    /**
     * 获取模板ID
     *
     * 与课程ID获取方式相同
     * 统一的事件参数处理模式
     */
    const templateId = e.currentTarget.dataset.id;

    /**
     * 页面跳转
     *
     * 双参数URL：同时传递模式和ID信息
     * 编辑页面可以准确识别当前的操作类型和目标对象
     */
    wx.navigateTo({ url: `/pages/course-edit/course-edit?mode=template&id=${templateId}` });
  },

  /**
   * onCourseCardTap: 课程卡片点击事件处理函数
   *
   * 功能：跳转到课程详情页面，查看课程的详细信息
   *
   * 业务场景：
   * 用户点击课程卡片时，查看课程的完整信息
   * 包括课程描述、预约学员、考勤记录等
   *
   * 数据传递：
   * e.currentTarget.dataset.course: 完整的课程对象
   * 比单独传递ID更高效，减少详情页的数据加载时间
   *
   * ID兼容性处理：
   * course._id || course.id: 处理不同数据源的ID字段差异
   * 确保在各种情况下都能正确获取课程ID
   */
  onCourseCardTap(e) {
    /**
     * 获取课程对象
     *
     * dataset.course: 完整的课程数据对象
     * 包含课程的所有信息，不仅仅是ID
     */
    const course = e.currentTarget.dataset.course;

    /**
     * 页面跳转
     *
     * 目标页面：course-detail（课程详情页）
     * 参数：课程ID，用于详情页的数据加载和状态管理
     *
     * ID字段兼容：
     * course._id: 云数据库的标准ID字段
     * course.id: 可能的自定义ID字段
     */
    wx.navigateTo({
      url: `/pages/course-detail/course-detail?id=${course._id || course.id}`,
    });
  },

  /**
   * toggleCollapse: 处理折叠面板展开/收起的事件处理函数
   *
   * 功能概述：
   * 控制课程卡片中预约学员信息的展开和收起状态
   * 支持多个课程独立的折叠状态管理
   *
   * 设计模式：
   * 使用函数式编程的map方法更新数组中的特定项目
   * 保持数据的不可变性，避免直接修改原数组
   *
   * 用户体验：
   * 提供按需查看详细信息的能力，节省屏幕空间
   * 类似于手风琴组件的交互模式
   *
   * @param {Object} e - 事件对象
   */
  toggleCollapse(e) {
    /**
     * 阻止事件冒泡
     *
     * 原因：
     * 折叠按钮位于课程卡片内部，如果不阻止冒泡
     * 点击折叠按钮会同时触发卡片的点击事件（跳转到详情页）
     *
     * 防御性编程：
     * 检查事件对象和方法是否存在，避免在某些环境下出错
     */
    if (e && e.stopPropagation) e.stopPropagation();

    /**
     * 获取课程ID
     *
     * 数据传递：
     * 通过WXML中的data-course-id属性传递课程ID
     * 在JavaScript中通过dataset.courseId获取
     *
     * 命名转换：
     * HTML中的data-course-id会自动转换为dataset.courseId
     * 这是小程序的标准数据传递机制
     */
    const courseId = e.currentTarget.dataset.courseId;

    /**
     * 课程列表更新函数
     *
     * 设计模式：高阶函数
     * 接收课程列表，返回更新后的课程列表
     * 不修改原数组，符合函数式编程原则
     *
     * 功能：更新指定课程的折叠状态
     *
     * @param {Array} courseList - 课程列表数组
     * @returns {Array} 更新后的课程列表
     */
    const updateCourseList = (courseList) => {
      /**
       * Array.map()方法：
       * 遍历数组中的每个元素，对每个元素执行转换函数
       * 返回新数组，不修改原数组
       *
       * 类似技术：
       * - C#: courseList.Select(course => ...)
       * - Java: courseList.stream().map(course -> ...)
       * - Python: [transform(course) for course in courseList]
       */
      return courseList.map(course => {
        /**
         * ID兼容性处理
         *
         * 支持不同的ID字段格式：
         * - course._id: 云数据库的标准ID字段
         * - course.id: 可能的自定义ID字段
         *
         * 逻辑或运算符：优先使用_id，如果不存在则使用id
         */
        const cid = course._id || course.id;

        /**
         * 目标课程检查
         *
         * 只更新与事件触发的课程ID匹配的课程
         * 其他课程保持原状不变
         */
        if (cid === courseId) {
          /**
           * 当前展开状态检查
           *
           * 判断逻辑：
           * 1. course.collapseValue存在且为数组
           * 2. 数组中包含'students'标识符
           *
           * 'students'标识符：
           * 表示学员信息区域的折叠状态
           * 支持未来扩展其他可折叠区域（如课程详情、评论等）
           */
          const isExpanded = course.collapseValue && course.collapseValue.includes('students');

          /**
           * 返回更新后的课程对象
           *
           * 扩展运算符：...course
           * 保留课程的所有原有属性
           *
           * collapseValue更新：
           * - 如果当前展开：设置为空数组（收起）
           * - 如果当前收起：设置为['students']（展开）
           *
           * 数组设计：
           * 使用数组而不是布尔值，支持多个区域的独立折叠控制
           */
          return {
            ...course,
            collapseValue: isExpanded ? [] : ['students']
          };
        }

        /**
         * 非目标课程：原样返回
         *
         * 保持其他课程的状态不变
         * 确保只影响用户点击的特定课程
         */
        return course;
      });
    };

    /**
     * 更新页面数据
     *
     * setData的回调机制：
     * 第二个参数是回调函数，在数据更新完成后执行
     * 确保DOM更新完成后再执行后续操作
     */
    this.setData({
      /**
       * 更新课程列表
       *
       * 防御性编程：this.data.courseList || []
       * 如果courseList不存在，使用空数组作为默认值
       * 避免因数据异常导致的程序错误
       */
      courseList: updateCourseList(this.data.courseList || [])
    }, () => {
      /**
       * 同步更新过滤列表
       *
       * 重要性：
       * 折叠状态的变化需要同步到filteredCourseList
       * 确保搜索结果中的课程也能正确显示折叠状态
       *
       * _filterAndSetCourses()：
       * 内部方法，重新应用搜索过滤条件
       * 保持搜索功能与折叠功能的协调工作
       */
      this._filterAndSetCourses();
    });
  },

  /**
   * onStudentSectionTap: 学员区域点击事件处理函数
   *
   * 功能：
   * 阻止学员区域的点击事件向上冒泡
   * 防止意外触发父级元素的点击事件
   *
   * 使用场景：
   * 当用户点击学员列表区域（非折叠按钮）时
   * 不应该触发课程卡片的点击事件（跳转详情页）
   *
   * 设计原理：
   * 事件冒泡机制：子元素的事件会向上传播到父元素
   * 通过stopPropagation()阻止这种传播
   *
   * @param {Object} e - 事件对象
   */
  onStudentSectionTap(e) {
    /**
     * 阻止事件冒泡
     *
     * 防御性编程：
     * 检查事件对象和stopPropagation方法是否存在
     * 在某些特殊环境下，事件对象可能不完整
     *
     * 用户体验：
     * 确保用户点击学员信息区域时不会意外跳转到详情页
     * 提供更精确的交互控制
     */
    if (e && e.stopPropagation) e.stopPropagation();
  },

  /**
   * onToggleCourseStatus: 课程状态切换事件处理函数
   *
   * 功能概述：
   * 处理课程上线/下线操作，是课程管理的核心功能之一
   * 支持不同状态间的安全切换，包含必要的确认和警告机制
   *
   * 业务规则：
   * 1. 上线操作：直接执行，风险较低
   * 2. 下线操作：需要确认，因为会影响已预约的用户
   *
   * 安全机制：
   * - 下线前警告用户影响范围
   * - 自动处理预约取消和退款
   * - 防止误操作的确认对话框
   *
   * 与您熟悉的技术对比：
   * 类似于数据库的事务操作，需要考虑数据一致性和回滚机制
   *
   * @param {Object} e - 事件对象，包含课程ID和当前状态
   */
  async onToggleCourseStatus(e) {
    /**
     * 提取事件数据
     *
     * 数据来源：WXML中的data-*属性
     * - data-id: 课程的唯一标识符
     * - data-status: 课程的当前状态
     *
     * 数据传递机制：
     * HTML: data-id="123" → JavaScript: dataset.id
     * 这是Web标准的数据传递方式
     */
    const courseId = e.currentTarget.dataset.id;
    const currentStatus = e.currentTarget.dataset.status;

    /**
     * 状态切换逻辑
     *
     * 三元运算符：condition ? value1 : value2
     * 实现状态的二元切换：
     * - 'online' → 'offline'（上线 → 下线）
     * - 'offline' → 'online'（下线 → 上线）
     * - 其他状态 → 'online'（兜底处理）
     */
    const newStatus = currentStatus === 'online' ? 'offline' : 'online';

    /**
     * 操作文本生成
     *
     * 用途：为用户界面提供清晰的操作描述
     * 虽然在当前代码中未直接使用，但为未来扩展预留
     *
     * 注意：IDE警告此变量未使用，但保留用于代码完整性
     */
    const actionText = newStatus === 'online' ? '上线' : '下线';

    /**
     * 下线操作的特殊处理
     *
     * 业务风险：
     * 下线操作会影响已预约的用户，需要特别谨慎
     * 包括：取消预约、退还费用、通知用户等
     */
    if (newStatus === 'offline') {
      /**
       * 下线确认对话框
       *
       * wx.showModal：小程序的模态对话框API
       * 类似于浏览器的confirm()，但功能更丰富
       *
       * 对话框配置：
       * - title: 对话框标题，简洁明了
       * - content: 详细说明操作后果和影响
       * - confirmText/cancelText: 自定义按钮文字
       * - success: 用户操作后的回调函数
       */
      wx.showModal({
        title: '下线课程',

        /**
         * 警告内容设计
         *
         * 信息要素：
         * 1. 操作后果：强制取消所有预约
         * 2. 补偿措施：退回考勤卡次数
         * 3. 确认询问：是否继续操作
         *
         * 用户体验：
         * 清晰告知操作的不可逆性和影响范围
         * 让用户在充分了解后果的基础上做决定
         */
        content: '下线将强制取消所有预约并退回考勤卡次数，是否继续？',

        confirmText: '确定',
        cancelText: '取消',

        /**
         * 用户选择处理
         *
         * success回调：无论用户选择确定还是取消都会执行
         * 需要通过res.confirm判断用户的具体选择
         *
         * 异步处理：
         * 使用async/await处理状态更新操作
         * 确保操作完成后再进行后续处理
         */
        success: async (res) => {
          /**
           * 确认操作处理
           *
           * res.confirm：用户点击确定按钮时为true
           * 只有用户明确确认后才执行实际的状态更新
           */
          if (res.confirm) {
            /**
             * 执行下线操作
             *
             * 参数说明：
             * - courseId: 要操作的课程ID
             * - newStatus: 新的状态值('offline')
             * - true: 表示需要处理预约取消（forceCancel参数）
             *
             * _updateCourseStatus：内部方法，处理实际的状态更新逻辑
             */
            await this._updateCourseStatus(courseId, newStatus, true);
          }
        }
      });
    } else {
      /**
       * 上线操作的直接处理
       *
       * 业务逻辑：
       * 上线操作风险较低，不会直接影响用户
       * 因此可以直接执行，无需额外确认
       *
       * 参数说明：
       * - courseId: 要操作的课程ID
       * - newStatus: 新的状态值('online')
       * - false: 不需要处理预约取消（forceCancel参数）
       */
      await this._updateCourseStatus(courseId, newStatus, false);
    }
  },

  /**
   * _checkAndToggleCourseStatus: 检查预约并切换课程状态的内部方法
   *
   * 功能概述：
   * 在执行状态切换前检查课程的预约情况
   * 根据预约状态提供不同的确认流程和用户提示
   *
   * 设计模式：
   * 私有方法（下划线前缀），不应被外部直接调用
   * 封装复杂的业务逻辑，提供清晰的职责分离
   *
   * 业务流程：
   * 1. 检查课程预约情况
   * 2. 根据预约状态显示不同的确认对话框
   * 3. 用户确认后执行实际的状态更新
   *
   * 错误处理：
   * 完整的try-catch-finally模式，确保用户体验的一致性
   *
   * @param {string} courseId - 课程ID
   * @param {string} newStatus - 新的课程状态
   * @param {string} actionText - 操作描述文字
   */
  async _checkAndToggleCourseStatus(courseId, newStatus, actionText) {
    /**
     * 显示加载提示
     *
     * 用户体验：
     * 数据库查询可能需要一定时间
     * 显示加载提示让用户知道系统正在处理
     * 避免用户因为没有反馈而重复操作
     */
    showLoading(this, '检查预约中...');

    /**
     * 异常处理包装
     *
     * try-catch模式：
     * 确保即使数据库操作失败，也能给用户适当的反馈
     * 避免程序崩溃或无响应状态
     */
    try {
      /**
       * 数据库连接和预约查询
       *
       * 查询目标：
       * 统计指定课程的有效预约数量
       * 用于判断状态切换的影响范围
       */
      const db = wx.cloud.database();

      /**
       * 预约记录查询
       *
       * 查询条件：
       * - courseId: 指定的课程ID
       * - status: 'upcoming' - 只查询有效的预约记录
       *
       * count()方法：
       * 只返回数量，不返回具体记录
       * 比get()方法更高效，减少数据传输量
       *
       * 类似SQL：
       * SELECT COUNT(*) FROM bookings
       * WHERE courseId = ? AND status = 'upcoming'
       */
      const bookingRes = await db.collection('bookings')
        .where({
          courseId: courseId,
          status: 'upcoming'
        })
        .count();

      /**
       * 隐藏加载提示
       *
       * 时机：数据查询完成后立即隐藏
       * 无论后续流程如何，都要清除loading状态
       */
      hideToast(this);

      /**
       * 根据预约情况分流处理
       *
       * 业务逻辑分支：
       * 1. 有预约：需要特别警告和二次确认
       * 2. 无预约：简单确认即可
       */
      if (bookingRes.total > 0) {
        /**
         * 有预约的情况：二次确认流程
         *
         * 风险提示：
         * 明确告知用户操作会影响已预约的学员
         * 说明系统会自动处理退款等后续事宜
         */
        wx.showModal({
          title: '确认下线',

          /**
           * 详细的影响说明
           *
           * 信息要素：
           * 1. 影响对象：所有已预约的学员
           * 2. 处理方式：强制取消预约
           * 3. 补偿措施：自动退还考勤卡次数
           * 4. 确认询问：是否确认执行
           *
           * 用户体验：
           * 让管理员充分了解操作后果
           * 减少因误解导致的客户投诉
           */
          content: `下线后，所有已预约的学员将被强制取消预约，并自动退还考勤卡次数。是否确认下线？`,

          success: async (res) => {
            if (res.confirm) {
              /**
               * 执行有预约的下线操作
               *
               * 参数说明：
               * - courseId: 课程ID
               * - newStatus: 新状态
               * - true: forceCancel参数，表示需要处理预约取消
               */
              await this._updateCourseStatus(courseId, newStatus, true);
            }
          }
        });
      } else {
        /**
         * 无预约的情况：简单确认流程
         *
         * 风险较低：
         * 没有预约意味着不会直接影响用户
         * 只需要简单的操作确认即可
         */
        wx.showModal({
          title: '确认操作',

          /**
           * 简单的操作确认
           *
           * 模板字符串：`确定要${actionText}该活动吗？`
           * 动态插入操作类型（上线/下线）
           * 提供清晰的操作描述
           */
          content: `确定要${actionText}该活动吗？`,

          success: async (res) => {
            if (res.confirm) {
              /**
               * 执行无预约的状态切换
               *
               * 参数说明：
               * - courseId: 课程ID
               * - newStatus: 新状态
               * - false: forceCancel参数，不需要处理预约取消
               */
              await this._updateCourseStatus(courseId, newStatus, false);
            }
          }
        });
      }
    } catch (error) {
      /**
       * 错误处理
       *
       * 清理工作：
       * 确保在出错时也要隐藏loading提示
       * 避免UI状态异常
       */
      hideToast(this);

      /**
       * 错误记录和用户反馈
       *
       * console.error：记录详细错误信息，便于调试
       * showMessage：向用户显示友好的错误提示
       *
       * 错误处理原则：
       * 1. 记录详细信息供开发者分析
       * 2. 向用户显示简洁易懂的提示
       * 3. 不暴露技术细节给最终用户
       */
      console.error('检查预约失败:', error);
      this.showMessage('检查预约失败', 'error');
    }
  },

  /**
   * refreshAllTabsAndCurrent: 刷新所有Tab缓存和当前Tab数据
   *
   * 功能概述：
   * 在执行重要操作（如删除、状态切换）后，清空所有缓存并重新加载数据
   * 确保用户看到的是最新、准确的数据状态
   *
   * 使用场景：
   * 1. 课程删除后：被删除的课程需要从所有tab中移除
   * 2. 状态切换后：课程可能在不同tab间移动
   * 3. 批量操作后：多个课程状态发生变化
   * 4. 数据同步后：确保本地缓存与服务器一致
   *
   * 设计理念：
   * 宁可多请求一次数据，也要确保数据的准确性
   * 避免因缓存不一致导致的用户困惑
   *
   * 性能考虑：
   * 虽然会清空所有缓存，但这类操作频率不高
   * 数据准确性比性能优化更重要
   */
  async refreshAllTabsAndCurrent() {
    /**
     * 重置所有Tab缓存
     *
     * 缓存清空策略：
     * 将所有tab的缓存重置为初始状态
     * 包括：空列表、第一页、有更多数据的标志
     *
     * 缓存结构说明：
     * {
     *   tabName: {
     *     list: [],           // 该tab的课程数据列表
     *     page: 1,            // 当前加载到的页码
     *     hasMore: true       // 是否还有更多数据可加载
     *   }
     * }
     *
     * 重置原因：
     * 1. 数据可能在不同tab间移动（如状态切换）
     * 2. 删除操作影响多个tab的数据
     * 3. 确保下次访问时重新加载最新数据
     */
    this.setData({
      courseTabCache: {
        /**
         * 各个tab的缓存重置
         *
         * all: 所有有效活动
         * online: 已上线活动
         * offline: 未上线活动
         * history: 历史活动
         *
         * 统一的初始状态：
         * - list: [] - 空数据列表
         * - page: 1 - 从第一页开始
         * - hasMore: true - 假设有数据可加载
         */
        all: { list: [], page: 1, hasMore: true },
        online: { list: [], page: 1, hasMore: true },
        offline: { list: [], page: 1, hasMore: true },
        history: { list: [], page: 1, hasMore: true }
      }
    });

    /**
     * 重新加载当前Tab数据
     *
     * loadCourseListPaged(true)：
     * - true参数表示重置加载（reset=true）
     * - 清空现有数据，从第一页重新开始
     * - 确保用户立即看到最新的数据
     *
     * await关键字：
     * 等待数据加载完成后再结束方法执行
     * 确保调用方知道刷新操作已完成
     *
     * 用户体验：
     * 操作完成后立即显示最新数据
     * 避免用户需要手动刷新或切换tab
     */
    await this.loadCourseListPaged(true);
  },

  /**
   * _updateCourseStatus: 更新课程状态的核心内部方法
   *
   * 功能概述：
   * 通过云函数安全地更新课程状态，并处理相关的业务逻辑
   * 包括预约取消、退款处理、数据同步等复杂操作
   *
   * 设计模式：
   * 私有方法（下划线前缀），封装复杂的业务逻辑
   * 统一的错误处理和用户反馈机制
   *
   * 安全考虑：
   * 使用云函数确保数据操作的安全性和一致性
   * 服务端处理敏感操作，避免客户端直接操作数据库
   *
   * 事务性：
   * 云函数内部使用事务确保数据一致性
   * 要么全部成功，要么全部回滚
   *
   * @param {string} courseId - 课程ID
   * @param {string} status - 新的课程状态 ('online' | 'offline')
   * @param {boolean} forceCancelBookings - 是否强制取消预约（默认false）
   */
  async _updateCourseStatus(courseId, status, forceCancelBookings = false) {
    /**
     * 显示操作进度
     *
     * 用户体验：
     * 状态更新可能涉及复杂的服务端操作
     * 显示loading让用户知道系统正在处理
     */
    showLoading(this, '操作中...');

    /**
     * 异常处理包装
     *
     * try-catch-finally结构：
     * - try: 执行主要的状态更新逻辑
     * - catch: 处理可能的错误情况
     * - finally: 无论成功失败都要清除loading状态
     */
    try {
      /**
       * 云函数调用
       *
       * 为什么使用云函数：
       * 1. 安全性：避免客户端直接操作敏感数据
       * 2. 一致性：服务端事务确保数据完整性
       * 3. 复杂性：预约取消、退款等复杂逻辑在服务端处理
       * 4. 权限：管理员操作需要服务端权限验证
       *
       * 与传统架构对比：
       * - 类似于调用RESTful API的PUT请求
       * - 类似于调用WCF服务或Web API
       * - 类似于RPC调用
       */
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          /**
           * action: 操作类型标识
           *
           * 'updateCourseStatus': 更新课程状态操作
           * 云函数内部会根据action分发到对应的处理逻辑
           */
          action: 'updateCourseStatus',

          /**
           * data: 操作参数
           *
           * 参数说明：
           * - courseId: 要更新的课程ID
           * - status: 新的状态值
           * - forceCancelBookings: 是否强制取消预约
           *
           * 参数设计：
           * 结构化传递，便于服务端解析和验证
           */
          data: {
            courseId: courseId,
            status: status,
            forceCancelBookings: forceCancelBookings
          }
        }
      });

      /**
       * 操作结果处理
       *
       * 云函数返回格式：
       * {
       *   result: {
       *     success: boolean,
       *     message?: string,
       *     cancelledCount?: number
       *   }
       * }
       */
      if (result.result && result.result.success) {
        /**
         * 成功消息构建
         *
         * 基础消息：根据操作类型生成
         * 扩展信息：根据具体操作结果添加详细信息
         */
        const actionText = status === 'online' ? '上线' : '下线';
        let message = `${actionText}成功`;

        /**
         * 预约取消信息补充
         *
         * 条件：
         * 1. forceCancelBookings为true（执行了预约取消）
         * 2. cancelledCount > 0（实际取消了预约）
         *
         * 用户价值：
         * 让管理员知道具体影响了多少用户
         * 便于后续的客户服务和沟通
         */
        if (forceCancelBookings && result.result.cancelledCount > 0) {
          message += `，已取消${result.result.cancelledCount}个预约并退还考勤卡次数`;
        }

        /**
         * 成功反馈和数据刷新
         *
         * showMessage：显示成功提示
         * refreshAllTabsAndCurrent：刷新所有数据
         *
         * 数据一致性：
         * 确保用户看到的数据与服务端状态一致
         */
        this.showMessage(message, 'success');
        await this.refreshAllTabsAndCurrent();
      } else {
        /**
         * 操作失败处理
         *
         * 可选链操作符：result.result?.message
         * 安全地访问可能不存在的属性
         * 避免因数据结构异常导致的程序错误
         *
         * 错误消息优先级：
         * 1. 服务端返回的具体错误信息
         * 2. 通用的"操作失败"提示
         */
        const errorMessage = result.result?.message || '操作失败';
        throw new Error(errorMessage);
      }
    } catch (error) {
      /**
       * 错误处理和用户反馈
       *
       * 错误记录：
       * console.error记录详细错误信息
       * 包含错误堆栈，便于开发者调试
       *
       * 用户提示：
       * 显示用户友好的错误信息
       * 包含操作类型，让用户明确哪个操作失败了
       */
      console.error('更新活动状态失败:', error);
      const actionText = status === 'online' ? '上线' : '下线';
      this.showMessage(`${actionText}失败: ${error.message}`, 'error');
    } finally {
      /**
       * 清理工作
       *
       * 无论操作成功还是失败，都要清除loading状态
       * 确保用户界面恢复正常，可以进行后续操作
       */
      hideToast(this);
    }
  },

  /**
   * onApplyTemplate: 应用模板创建新活动的事件处理函数
   *
   * 功能概述：
   * 基于现有模板快速创建新的课程活动
   * 提供标准化的课程创建流程，提升管理效率
   *
   * 业务价值：
   * 1. 效率提升：避免重复输入相同的课程信息
   * 2. 标准化：确保课程信息的一致性和规范性
   * 3. 减少错误：预设的模板减少手动输入错误
   * 4. 快速复制：成功的课程配置可以快速复用
   *
   * 用户体验：
   * 提供确认对话框，避免误操作
   * 让用户明确知道即将执行的操作
   *
   * @param {Object} e - 事件对象，包含模板ID
   */
  async onApplyTemplate(e) {
    /**
     * 获取模板ID
     *
     * 数据传递：
     * 通过WXML中的data-id属性传递模板ID
     * 在JavaScript中通过dataset.id获取
     */
    const templateId = e.currentTarget.dataset.id;

    /**
     * 应用确认对话框
     *
     * 用户体验考虑：
     * 1. 明确操作意图：让用户知道要做什么
     * 2. 防止误操作：避免意外点击导致的操作
     * 3. 提供选择权：用户可以取消操作
     *
     * 对话框设计：
     * - 简洁的标题：直接说明操作类型
     * - 清晰的内容：说明操作结果
     * - 标准的按钮：确定/取消选项
     */
    wx.showModal({
      title: '确认应用',
      content: '确定要应用该模板创建新活动吗？',
      success: async (res) => {
        /**
         * 用户确认处理
         *
         * res.confirm：用户点击确定按钮时为true
         * 只有用户明确确认后才执行模板应用操作
         */
        if (res.confirm) {
          /**
           * 执行模板应用
           *
           * 调用内部方法处理具体的应用逻辑
           * 分离用户交互和业务逻辑，提高代码可维护性
           */
          await this._applyTemplate(templateId);
        }
      }
    });
  },

  /**
   * _applyTemplate: 应用模板的内部实现方法
   *
   * 功能概述：
   * 获取模板数据并跳转到编辑页面进行课程创建
   * 处理模板应用过程中的各种异常情况
   *
   * 设计模式：
   * 私有方法（下划线前缀），封装具体的业务逻辑
   * 与用户交互逻辑分离，便于单独测试和维护
   *
   * 实现策略：
   * 不在当前页面处理模板数据，而是跳转到专门的编辑页面
   * 编辑页面负责模板数据的解析和预填充
   *
   * @param {string} templateId - 要应用的模板ID
   */
  async _applyTemplate(templateId) {
    /**
     * 显示操作进度
     *
     * 用户体验：
     * 模板数据获取和页面跳转可能需要一定时间
     * 显示loading提示让用户知道系统正在处理
     */
    showLoading(this, '应用模板中...');

    /**
     * 异常处理包装
     *
     * try-catch-finally结构：
     * 确保即使操作失败，也能给用户适当的反馈
     */
    try {
      /**
       * 模板数据验证
       *
       * 业务逻辑：
       * 在跳转前验证模板是否存在
       * 避免跳转到编辑页面后发现模板不存在
       *
       * 数据库查询：
       * 使用doc(templateId).get()获取特定模板
       * 比where查询更高效，直接通过ID定位
       */
      const db = wx.cloud.database();
      const templateRes = await db.collection('coursesTemplate').doc(templateId).get();
      const template = templateRes.data;

      /**
       * 模板存在性检查
       *
       * 防御性编程：
       * 检查模板数据是否存在
       * 避免因数据异常导致的后续错误
       */
      if (!template) {
        throw new Error('模板不存在');
      }

      /**
       * 页面跳转
       *
       * 导航策略：
       * 跳转到课程编辑页面，通过URL参数传递信息
       *
       * URL参数说明：
       * - mode=apply: 告知编辑页面这是模板应用模式
       * - templateId=${templateId}: 传递模板ID
       *
       * 编辑页面职责：
       * 1. 根据mode参数识别操作类型
       * 2. 根据templateId加载模板数据
       * 3. 预填充表单字段
       * 4. 提供时间等必填信息的输入
       *
       * 设计优势：
       * 1. 职责分离：当前页面负责模板选择，编辑页面负责数据处理
       * 2. 代码复用：编辑页面可以处理多种创建模式
       * 3. 用户体验：在专门的编辑界面完成课程创建
       */
      wx.navigateTo({
        url: `/pages/course-edit/course-edit?mode=apply&templateId=${templateId}`
      });

    } catch (error) {
      /**
       * 错误处理
       *
       * 错误记录：
       * console.error记录详细错误信息
       * 便于开发者调试和问题排查
       *
       * 用户反馈：
       * 显示简洁的错误提示
       * 不暴露技术细节给用户
       */
      console.error('应用模板失败:', error);
      this.showMessage('应用模板失败', 'error');
    } finally {
      /**
       * 清理工作
       *
       * 无论操作成功还是失败，都要清除loading状态
       * 确保用户界面恢复正常
       */
      hideToast(this);
    }
  },

  async _deleteCourseAndBookings(courseId) {
    showLoading(this, '删除中...');
    try {
      const res = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'deleteCourse',
          data: { _id: courseId }
        }
      });
      const result = res.result || {};
      if (result.success) {
        this.showMessage('删除成功', 'success');
        await this.refreshAllTabsAndCurrent();
      } else {
        this.showMessage(result.message || '删除失败', 'error');
      }
    } catch (err) {
      this.showMessage('删除失败', 'error');
    } finally {
      hideToast(this);
    }
  },

  async onDeleteCourse(e) {
    const courseId = e.currentTarget.dataset.id;
    const db = wx.cloud.database();
    showLoading(this, '检查预约...');
    try {
      const bookingRes = await db.collection('bookings')
        .where({ courseId, status: 'upcoming' })
        .count();
      hideToast(this);
      if (bookingRes.total > 0) {
        wx.showModal({
          title: '无法删除',
          content: `该活动已有${bookingRes.total}个预约，不能删除。请先让学员取消预约。`,
          showCancel: false
        });
        return;
      }
      wx.showModal({
        title: '确认删除',
        content: '确定要删除该活动吗？',
        success: async (res) => {
          if (res.confirm) {
            await this._deleteCourseAndBookings(courseId);
          }
        }
      });
    } catch (err) {
      hideToast(this);
      this.showMessage('检查预约失败', 'error');
    }
  },

  // 删除模板
  async onDeleteTemplate(e) {
    const templateId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认删除',
      content: '确定要删除该模板吗？',
      success: async (res) => {
        if (res.confirm) {
          await this._deleteTemplate(templateId);
        }
      }
    });
  },

  async _deleteTemplate(templateId) {
    showLoading(this, '删除中...');
    try {
      const db = wx.cloud.database();
      await db.collection('coursesTemplate').doc(templateId).remove();
      this.showMessage('删除成功', 'success');
      await this.loadTemplateList();
    } catch (error) {
      console.error('删除模板失败:', error);
      this.showMessage('删除失败', 'error');
    } finally {
      hideToast(this);
    }
  },

  // 显示消息提示
  showMessage(content, theme = 'info') {
    showToast(this, { message: content, theme });
  },

  // 搜索输入变化
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value }, () => {
      this._filterAndSetCourses();
    });
  },

  // 搜索输入框失去焦点时，如果没有搜索内容则自动收起
  onSearchBlur() {
    if (!this.data.searchValue) {
      setTimeout(() => {
        this.setData({ searchExpanded: false });
      }, 200); // 延迟收起，避免与其他操作冲突
    }
  },

  // 展开搜索框
  onExpandSearch() {
    this.setData({
      searchExpanded: true
    });
  },

  // 收起搜索框
  onCollapseSearch() {
    this.setData({
      searchExpanded: false
    });
  },

  // 清空搜索
  onSearchClear() {
    this.setData({ searchValue: '' }, this._filterAndSetCourses);
  },

  // 提交搜索
  onSearchSubmit(e) {
    this.setData({ searchValue: e.detail.value }, this._filterAndSetCourses);
  },

  // 模板搜索相关方法
  // 展开模板搜索框
  onExpandTemplateSearch() {
    this.setData({
      templateSearchExpanded: true
    });
  },

  // 收起模板搜索框
  onCollapseTemplateSearch() {
    this.setData({
      templateSearchExpanded: false
    });
  },

  // 模板搜索输入变化
  onTemplateSearchInput(e) {
    const value = e.detail.value;
    this.setData({ templateSearchValue: value }, () => {
      this._filterAndSetTemplates();
    });
  },

  // 模板搜索输入框失去焦点时，如果没有搜索内容则自动收起
  onTemplateSearchBlur() {
    if (!this.data.templateSearchValue) {
      setTimeout(() => {
        this.setData({ templateSearchExpanded: false });
      }, 200); // 延迟收起，避免与其他操作冲突
    }
  },

  // 清空模板搜索
  onTemplateSearchClear() {
    this.setData({ templateSearchValue: '' }, this._filterAndSetTemplates);
  },

  // 提交模板搜索
  onTemplateSearchSubmit(e) {
    this.setData({ templateSearchValue: e.detail.value }, this._filterAndSetTemplates);
  },

  // 过滤并设置模板列表
  _filterAndSetTemplates() {
    const { templateSearchValue, allTemplateList } = this.data;

    // 使用全部模板列表作为基础数据源
    let baseTemplateList = allTemplateList || [];

    // 如果没有搜索关键词，显示全部数据
    if (!templateSearchValue) {
      this.setData({ templateList: baseTemplateList });
      return;
    }

    // 搜索过滤
    const keyword = templateSearchValue.toLowerCase();
    const filteredTemplates = baseTemplateList.filter(template => {
      return (
        (template.name && template.name.toLowerCase().includes(keyword)) ||
        (template.venue && template.venue.toLowerCase().includes(keyword))
      );
    });

    this.setData({ templateList: filteredTemplates });
  },

  /**
   * updateVisibleTemplateList: 更新可见模板列表
   *
   * 功能：根据当前筛选状态和搜索条件更新可见模板列表
   * 调用时机：模板数据加载完成后、筛选条件变化后
   */
  updateVisibleTemplateList() {
    const { templateList, templateSearchKeyword } = this.data;
    let filteredList = [...templateList];

    // 根据搜索关键词筛选
    if (templateSearchKeyword && templateSearchKeyword.trim()) {
      const keyword = templateSearchKeyword.trim().toLowerCase();
      filteredList = filteredList.filter(template => {
        return (
          (template.name && template.name.toLowerCase().includes(keyword)) ||
          (template.venue && template.venue.toLowerCase().includes(keyword))
        );
      });
    }

    console.log('更新可见模板列表:', {
      searchKeyword: templateSearchKeyword,
      totalCount: templateList.length,
      filteredCount: filteredList.length
    });

    this.setData({
      visibleTemplateList: filteredList
    });
  },

  /**
   * showBatchDeleteConfirmDialog: 显示批量删除确认对话框
   *
   * 功能：显示5秒倒计时确认对话框，支持课程和模板的批量删除
   * 参数：selectedIds - 选中的ID列表
   * 参数：type - 删除类型（'course' 或 'template'）
   */
  showBatchDeleteConfirmDialog(selectedIds, type = 'course') {
    const isTemplate = type === 'template';
    const itemName = isTemplate ? '模板' : '活动';
    const count = selectedIds.length;

    console.log(`显示批量删除${itemName}确认对话框:`, selectedIds);

    // 显示确认对话框
    wx.showModal({
      title: `批量删除${itemName}`,
      content: `确定要删除选中的 ${count} 个${itemName}吗？此操作不可撤销。`,
      confirmText: '确定删除',
      confirmColor: '#ff4757',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认删除，开始5秒倒计时
          this.startBatchDeleteCountdown(selectedIds, type);
        } else {
          console.log(`用户取消批量删除${itemName}`);
        }
      }
    });
  },

  /**
   * startBatchDeleteCountdown: 开始批量删除倒计时
   *
   * 功能：显示5秒倒计时，用户可以在倒计时期间取消操作
   * 参数：selectedIds - 选中的ID列表
   * 参数：type - 删除类型（'course' 或 'template'）
   */
  startBatchDeleteCountdown(selectedIds, type = 'course') {
    const isTemplate = type === 'template';
    const itemName = isTemplate ? '模板' : '活动';
    let countdown = 5;

    console.log(`开始批量删除${itemName}倒计时:`, countdown);

    // 显示倒计时对话框
    const showCountdownDialog = () => {
      wx.showModal({
        title: `删除${itemName}倒计时`,
        content: `${countdown} 秒后将删除选中的${itemName}，点击取消可停止删除。`,
        confirmText: '立即删除',
        confirmColor: '#ff4757',
        cancelText: '取消删除',
        success: (res) => {
          if (res.confirm) {
            // 用户选择立即删除
            console.log(`用户选择立即删除${itemName}`);
            this.executeBatchDelete(selectedIds, type);
          } else {
            // 用户取消删除
            console.log(`用户取消删除${itemName}`);
            showToast(this, {
              message: `已取消删除${itemName}`,
              theme: 'success',
              duration: 1500
            });
          }
        }
      });
    };

    // 开始倒计时
    const timer = setInterval(() => {
      countdown--;
      console.log(`批量删除${itemName}倒计时:`, countdown);

      if (countdown <= 0) {
        // 倒计时结束，执行删除
        clearInterval(timer);
        console.log(`倒计时结束，执行批量删除${itemName}`);
        this.executeBatchDelete(selectedIds, type);
      } else {
        // 更新倒计时显示
        showCountdownDialog();
      }
    }, 1000);

    // 显示初始倒计时对话框
    showCountdownDialog();
  },

  /**
   * executeBatchDelete: 执行批量删除
   *
   * 功能：实际执行批量删除操作
   * 参数：selectedIds - 选中的ID列表
   * 参数：type - 删除类型（'course' 或 'template'）
   */
  async executeBatchDelete(selectedIds, type = 'course') {
    const isTemplate = type === 'template';
    const itemName = isTemplate ? '模板' : '活动';

    console.log(`执行批量删除${itemName}:`, selectedIds);

    try {
      // 显示加载状态
      wx.showLoading({
        title: `正在删除${itemName}...`,
        mask: true
      });

      if (isTemplate) {
        // 删除模板
        await this.deleteTemplatesById(selectedIds);

        // 清空选中状态
        this.setData({
          selectedTemplateIds: [],
          templateBatchMode: false
        });

        // 重新加载模板列表
        await this.loadTemplateList();
      } else {
        // 删除课程（原有逻辑）
        await this.deleteCoursesById(selectedIds);

        // 清空选中状态
        this.setData({
          selectedCourseIds: [],
          batchMode: false
        });

        // 重新加载课程列表
        await this.loadCourseListPaged(true);
      }

      wx.hideLoading();

      // 显示成功提示
      showToast(this, {
        message: `成功删除 ${selectedIds.length} 个${itemName}`,
        theme: 'success',
        duration: 2000
      });

    } catch (error) {
      wx.hideLoading();
      console.error(`批量删除${itemName}失败:`, error);

      showToast(this, {
        message: `删除${itemName}失败，请重试`,
        theme: 'error',
        duration: 2000
      });
    }
  },

  /**
   * deleteTemplatesById: 根据ID删除模板
   *
   * 功能：批量删除指定ID的模板
   * 参数：templateIds - 模板ID列表
   */
  async deleteTemplatesById(templateIds) {
    console.log('删除模板:', templateIds);

    // 这里应该调用云函数或数据库操作来删除模板
    // 暂时使用模拟实现
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('模板删除完成');
        resolve();
      }, 1000);
    });
  },

  /**
   * 批量操作相关方法
   */

  // 切换批量模式
  onToggleBatchMode() {
    const newBatchMode = !this.data.batchMode;
    console.log('切换批量模式:', newBatchMode);

    // 更新课程列表，清空所有选择状态
    const updatedCourseList = this.data.visibleCourseList.map(course => ({
      ...course,
      isSelected: false
    }));

    this.setData({
      batchMode: newBatchMode,
      selectedCourseIds: [], // 切换模式时清空选择
      visibleCourseList: updatedCourseList
    }, () => {
      console.log('批量模式状态更新完成:', this.data.batchMode);
    });
  },

  // 选择/取消选择课程
  onCourseSelect(e) {
    const courseId = e.currentTarget.dataset.courseId;
    if (!courseId) {
      console.error('课程ID不存在');
      return;
    }

    this._toggleCourseSelection(courseId);
  },



  // 切换课程选择状态的通用方法
  _toggleCourseSelection(courseId) {
    const { selectedCourseIds, visibleCourseList } = this.data;

    let newSelectedIds;
    if (selectedCourseIds.includes(courseId)) {
      // 取消选择
      newSelectedIds = selectedCourseIds.filter(id => id !== courseId);
    } else {
      // 添加选择
      newSelectedIds = [...selectedCourseIds, courseId];
    }

    // 更新课程列表中的选择状态
    const updatedCourseList = visibleCourseList.map(course => ({
      ...course,
      isSelected: newSelectedIds.includes(course._id)
    }));

    console.log('更新选择状态:', {
      courseId,
      oldSelected: selectedCourseIds,
      newSelected: newSelectedIds
    });

    // 使用回调确保状态更新完成
    this.setData({
      selectedCourseIds: newSelectedIds,
      visibleCourseList: updatedCourseList
    }, () => {
      console.log('选择状态更新完成:', this.data.selectedCourseIds);
    });
  },

  // 批量上线
  onBatchOnline() {
    this._showBatchConfirm('online', '批量上线');
  },

  // 批量下线
  onBatchOffline() {
    this._showBatchConfirm('offline', '批量下线');
  },

  // 批量删除
  onBatchDelete() {
    // 检查是否有已预约的课程
    const selectedCourses = this._getSelectedCourses();
    const bookedCourses = selectedCourses.filter(course => course.bookedCount > 0);

    if (bookedCourses.length > 0) {
      this.showMessage(`有 ${bookedCourses.length} 个活动已被学员预约，无法删除`, 'error');
      return;
    }

    this._showBatchConfirm('delete', '批量删除');
  },

  // 显示批量操作确认对话框
  _showBatchConfirm(operationType, operationName) {
    const count = this.data.selectedCourseIds.length;
    const content = `确定要${operationName} ${count} 个活动吗？此操作不可撤销。`;

    this.setData({
      batchOperationType: operationType,
      batchConfirmDialogVisible: true,
      batchConfirmContent: content,
      batchCountdown: 5,
      batchConfirmBtn: `确认${operationName}(5)`
    });

    // 开始倒计时
    this._startBatchCountdown();
  },

  // 开始倒计时
  _startBatchCountdown() {
    const timer = setInterval(() => {
      const countdown = this.data.batchCountdown - 1;
      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          batchCountdown: 0,
          batchConfirmBtn: `确认${this._getOperationName()}`
        });
      } else {
        this.setData({
          batchCountdown: countdown,
          batchConfirmBtn: `确认${this._getOperationName()}(${countdown})`
        });
      }
    }, 1000);
  },

  // 获取操作名称
  _getOperationName() {
    const { batchOperationType } = this.data;
    switch (batchOperationType) {
      case 'online': return '批量上线';
      case 'offline': return '批量下线';
      case 'delete': return '批量删除';
      default: return '批量操作';
    }
  },

  // 获取选中的课程对象
  _getSelectedCourses() {
    const { selectedCourseIds, visibleCourseList } = this.data;
    return visibleCourseList.filter(course => selectedCourseIds.includes(course._id));
  },

  // 批量操作确认
  onBatchConfirm() {
    if (this.data.batchCountdown > 0) {
      this.showMessage('请等待倒计时结束', 'warning');
      return;
    }

    this._executeBatchOperation();
  },

  // 批量操作取消
  onBatchCancel() {
    this.setData({
      batchConfirmDialogVisible: false,
      batchOperationType: '',
      batchCountdown: 5
    });
  },

  // 执行批量操作
  async _executeBatchOperation() {
    const { batchOperationType, selectedCourseIds } = this.data;

    if (selectedCourseIds.length === 0) {
      this.showMessage('请选择要操作的活动', 'warning');
      return;
    }

    // 关闭确认对话框
    this.setData({ batchConfirmDialogVisible: false });

    showLoading(this, '批量操作中...');

    try {
      let successCount = 0;
      let failCount = 0;

      // 逐个执行操作
      for (const courseId of selectedCourseIds) {
        try {
          await this._executeSingleBatchOperation(courseId, batchOperationType);
          successCount++;
        } catch (error) {
          console.error(`批量操作失败 - 课程ID: ${courseId}`, error);
          failCount++;
        }
      }

      // 显示结果
      if (failCount === 0) {
        this.showMessage(`批量操作成功，共处理 ${successCount} 个活动`, 'success');
      } else {
        this.showMessage(`批量操作完成，成功 ${successCount} 个，失败 ${failCount} 个`, 'warning');
      }

      // 刷新数据
      await this.loadCourseListPaged(true);

      // 退出批量模式
      this.setData({
        batchMode: false,
        selectedCourseIds: [],
        batchOperationType: ''
      });

    } catch (error) {
      console.error('批量操作失败:', error);
      this.showMessage('批量操作失败', 'error');
    } finally {
      hideToast(this);
    }
  },

  // 执行单个批量操作
  async _executeSingleBatchOperation(courseId, operationType) {
    const result = await wx.cloud.callFunction({
      name: 'adminManagement',
      data: {
        action: 'batchOperation',
        data: {
          courseId,
          operationType
        }
      }
    });

    if (!result.result || !result.result.success) {
      throw new Error(result.result?.message || '操作失败');
    }

    return result.result;
  },

  // 过滤并设置活动列表
  _filterAndSetCourses() {
    let filtered = this.data.courseList;

    // 根据搜索关键词过滤
    if (this.data.searchValue) {
      const val = this.data.searchValue.trim();
      filtered = filtered.filter(course => {
        return (
          (course.name && course.name.indexOf(val) !== -1) ||
          (course.coachDisplay && course.coachDisplay.indexOf(val) !== -1) ||
          (course.venue && course.venue.indexOf(val) !== -1)
        );
      });
    }

    // 使用时间轴模式，进行时间分组
    this._groupCoursesByTime(filtered);

    // 保持兼容性，同时设置filteredCourseList
    this.setData({ filteredCourseList: filtered });
  },

  /**
   * onRemoveStudent: 移除学员预约的事件处理函数
   *
   * 功能概述：
   * 管理员为学员取消预约，不受时间限制
   * 即使活动已结束也可以操作
   *
   * 权限要求：
   * 仅管理员可用，需要在UI层面进行权限控制
   *
   * 安全机制：
   * 5秒倒计时确认，防止误操作
   */
  onRemoveStudent(e) {
    // 获取事件数据
    const { courseId, studentOpenid, studentName, bookingId } = e.currentTarget.dataset;

    // 验证必要参数
    if (!courseId || !studentOpenid || !bookingId) {
      this.showMessage('参数错误，无法移除学员', 'error');
      return;
    }

    // 存储移除数据
    this.setData({
      removeStudentData: {
        courseId,
        studentOpenid,
        studentName: studentName || '未知学员',
        bookingId
      }
    });

    // 显示确认对话框
    this._showRemoveStudentDialog();
  },

  /**
   * _showRemoveStudentDialog: 显示移除学员确认对话框
   *
   * 功能：
   * 显示带倒计时的确认对话框
   * 提供退还考勤卡次数的选项
   */
  _showRemoveStudentDialog() {
    const { studentName } = this.data.removeStudentData;

    // 重置倒计时
    this.setData({
      removeStudentCountdown: 5,
      removeStudentDialogVisible: true,
      removeStudentDialogContent: `确定要为学员"${studentName}"取消预约吗？\n\n此操作不受时间限制，即使活动已结束也可以操作。\n系统将自动退还考勤卡次数。\n\n倒计时：5秒`,
      removeStudentDialogConfirmBtn: '确认移除(5)'
    });

    // 启动倒计时
    this._startRemoveStudentCountdown();
  },

  /**
   * _startRemoveStudentCountdown: 启动移除学员倒计时
   *
   * 功能：
   * 每秒更新倒计时数字和按钮文字
   * 倒计时结束后允许确认操作
   */
  _startRemoveStudentCountdown() {
    // 清除可能存在的定时器
    this._clearRemoveStudentCountdown();

    // 创建新的定时器
    this.data.removeStudentCountdownTimer = setInterval(() => {
      const countdown = this.data.removeStudentCountdown - 1;
      const { studentName } = this.data.removeStudentData;

      if (countdown > 0) {
        // 继续倒计时
        this.setData({
          removeStudentCountdown: countdown,
          removeStudentDialogContent: `确定要为学员"${studentName}"取消预约吗？\n\n此操作不受时间限制，即使活动已结束也可以操作。\n系统将自动退还考勤卡次数。\n\n倒计时：${countdown}秒`,
          removeStudentDialogConfirmBtn: `确认移除(${countdown})`
        });
      } else {
        // 倒计时结束
        this.setData({
          removeStudentCountdown: 0,
          removeStudentDialogContent: `确定要为学员"${studentName}"取消预约吗？\n\n此操作不受时间限制，即使活动已结束也可以操作。\n系统将自动退还考勤卡次数。`,
          removeStudentDialogConfirmBtn: '确认移除'
        });
        this._clearRemoveStudentCountdown();
      }
    }, 1000);
  },

  /**
   * _clearRemoveStudentCountdown: 清除移除学员倒计时定时器
   *
   * 功能：
   * 清除定时器，防止内存泄漏
   * 在对话框关闭或确认操作时调用
   */
  _clearRemoveStudentCountdown() {
    if (this.data.removeStudentCountdownTimer) {
      clearInterval(this.data.removeStudentCountdownTimer);
      this.setData({ removeStudentCountdownTimer: null });
    }
  },

  /**
   * onRemoveStudentConfirm: 确认移除学员
   *
   * 功能：
   * 执行实际的移除操作
   * 调用云函数取消预约并退还考勤卡次数
   */
  async onRemoveStudentConfirm() {
    // 检查倒计时是否结束
    if (this.data.removeStudentCountdown > 0) {
      this.showMessage('请等待倒计时结束', 'warning');
      return;
    }

    // 获取移除数据
    const { courseId, studentOpenid, bookingId, studentName } = this.data.removeStudentData;

    try {
      // 显示加载提示
      showLoading(this, '移除中...');

      // 调用云函数执行移除操作
      const result = await wx.cloud.callFunction({
        name: 'bookingManagement',
        data: {
          action: 'adminCancelBooking',
          data: {
            bookingId,
            courseId,
            studentOpenid,
            refundCard: true // 退还考勤卡次数
          }
        }
      });

      if (result.result && result.result.success) {
        // 移除成功
        this.showMessage(`已为学员"${studentName}"取消预约`, 'success');

        // 关闭对话框
        this.onRemoveStudentCancel();

        // 刷新课程列表
        await this.refreshAllTabsAndCurrent();
      } else {
        // 移除失败
        this.showMessage(result.result?.message || '移除失败', 'error');
      }
    } catch (error) {
      console.error('移除学员预约失败:', error);
      this.showMessage('移除失败: ' + (error.message || '网络错误'), 'error');
    } finally {
      hideToast(this);
    }
  },

  /**
   * onRemoveStudentCancel: 取消移除学员操作
   *
   * 功能：
   * 关闭确认对话框
   * 清理相关状态和定时器
   */
  onRemoveStudentCancel() {
    // 清除倒计时定时器
    this._clearRemoveStudentCountdown();

    // 重置对话框状态
    this.setData({
      removeStudentDialogVisible: false,
      removeStudentDialogContent: '',
      removeStudentDialogConfirmBtn: '确认移除(5)',
      removeStudentCountdown: 5,
      removeStudentData: null
    });
  },

  // 按时间分组活动数据 - 参考my-bookings的实现
  _groupCoursesByTime(courseList) {
    if (!courseList || courseList.length === 0) {
      this.setData({
        allFutureCourses: [],
        allHistoryCourses: [],
        visibleCourseList: [],
        noMoreHistory: true,
        noMoreFuture: true
      });
      return;
    }

    const now = new Date();

    const futureCourses = [];
    const historyCourses = [];

    courseList.forEach(course => {
      if (course.startTime && course.endTime) {
        // 使用结束时间判断课程是否已结束，与云函数逻辑保持一致
        const courseEndTime = new Date(course.endTime);

        if (courseEndTime < now) {
          // 课程已结束，放入历史课程
          historyCourses.push(course);
        } else {
          // 课程未结束，放入未来课程
          futureCourses.push(course);
        }
      } else if (course.startTime) {
        // 如果没有结束时间，使用开始时间判断（兼容旧数据）
        const courseDate = new Date(course.startTime);
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const courseDateOnly = new Date(courseDate.getFullYear(), courseDate.getMonth(), courseDate.getDate());

        if (courseDateOnly >= today) {
          futureCourses.push(course);
        } else {
          historyCourses.push(course);
        }
      } else {
        // 没有时间信息的活动默认放到未来
        futureCourses.push(course);
      }
    });

    // 排序
    futureCourses.sort((a, b) => {
      const timeA = a.startTime ? new Date(a.startTime).getTime() : 0;
      const timeB = b.startTime ? new Date(b.startTime).getTime() : 0;
      return timeA - timeB; // 未来活动：最早的在前
    });

    historyCourses.sort((a, b) => {
      const timeA = a.startTime ? new Date(a.startTime).getTime() : 0;
      const timeB = b.startTime ? new Date(b.startTime).getTime() : 0;
      return timeB - timeA; // 历史活动：最近的在前
    });

    // 如果有搜索条件，显示所有匹配结果；否则分页显示
    let initialVisible;
    if (this.data.searchValue && this.data.searchValue.trim()) {
      // 搜索模式：显示所有匹配结果
      initialVisible = [...historyCourses, ...futureCourses];
      // 初始化选择状态
      initialVisible = initialVisible.map(course => ({
        ...course,
        isSelected: false
      }));
      this.setData({
        allFutureCourses: futureCourses,
        allHistoryCourses: historyCourses,
        visibleCourseList: initialVisible,
        noMoreHistory: true,
        noMoreFuture: true
      });
    } else {
      // 正常模式：根据当前tab决定显示什么数据
      if (this.data.courseActiveTab === 'history') {
        // 历史活动tab：显示历史活动
        initialVisible = historyCourses.slice(0, this.data.pageSize);
        // 初始化选择状态
        initialVisible = initialVisible.map(course => ({
          ...course,
          isSelected: false
        }));
        this.setData({
          allFutureCourses: futureCourses,
          allHistoryCourses: historyCourses,
          visibleCourseList: initialVisible,
          noMoreHistory: initialVisible.length >= historyCourses.length,
          noMoreFuture: true // 历史tab不需要加载未来数据
        });
      } else {
        // 其他tab：显示未来活动
        initialVisible = futureCourses.slice(0, this.data.pageSize);
        // 初始化选择状态
        initialVisible = initialVisible.map(course => ({
          ...course,
          isSelected: false
        }));
        this.setData({
          allFutureCourses: futureCourses,
          allHistoryCourses: historyCourses,
          visibleCourseList: initialVisible,
          noMoreHistory: historyCourses.length === 0,
          noMoreFuture: initialVisible.length >= futureCourses.length
        });
      }
    }
  }
});

// 注册过滤器
if (typeof wx !== 'undefined' && wx.canIUse && wx.canIUse('nextTick')) {
  wx.nextTick(() => {
    if (typeof getApp === 'function' && getApp().globalData) {
      getApp().globalData.formatDate = formatDate;
      getApp().globalData.formatTime = formatTime;
    }
  });
}