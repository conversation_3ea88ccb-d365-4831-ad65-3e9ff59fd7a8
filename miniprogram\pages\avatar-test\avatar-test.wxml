<!--avatar-test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">头像保存功能测试</text>
    <text class="subtitle">诊断头像保存问题</text>
  </view>

  <!-- 用户信息显示 -->
  <view class="user-info" wx:if="{{userInfo}}">
    <text class="section-title">当前用户信息</text>
    <view class="info-item">
      <text class="label">昵称:</text>
      <text class="value">{{userInfo.nickName || '未设置'}}</text>
    </view>
    <view class="info-item">
      <text class="label">头像:</text>
      <text class="value">{{userInfo.avatarUrl ? '已设置' : '未设置'}}</text>
    </view>
    <view class="info-item" wx:if="{{userInfo.avatarUrl}}">
      <image src="{{userInfo.avatarUrl}}" class="avatar-preview" mode="aspectFill"/>
    </view>
  </view>

  <!-- 测试头像预览 -->
  <view class="test-avatar" wx:if="{{avatarUrl}}">
    <text class="section-title">测试头像</text>
    <image src="{{avatarUrl}}" class="avatar-preview" mode="aspectFill"/>
    <text class="avatar-url">{{avatarUrl}}</text>
  </view>

  <!-- 测试按钮 -->
  <view class="test-buttons">
    <t-button 
      theme="primary" 
      size="large"
      block
      bind:tap="runFullTest"
      loading="{{isRunning && currentTest === '完整测试'}}"
      disabled="{{isRunning}}"
    >
      运行完整测试
    </t-button>
    
    <view class="button-row">
      <t-button 
        theme="default" 
        size="medium"
        bind:tap="onTestDatabase"
        loading="{{isRunning && currentTest === '数据库访问测试'}}"
        disabled="{{isRunning}}"
      >
        测试数据库
      </t-button>
      
      <t-button 
        theme="default" 
        size="medium"
        bind:tap="onTestUpload"
        loading="{{isRunning && currentTest === '头像上传测试'}}"
        disabled="{{isRunning}}"
      >
        测试上传
      </t-button>
      
      <t-button 
        theme="default" 
        size="medium"
        bind:tap="onTestSave"
        loading="{{isRunning && currentTest === '头像保存测试'}}"
        disabled="{{isRunning || !avatarUrl}}"
      >
        测试保存
      </t-button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="test-results">
    <view class="results-header">
      <text class="section-title">测试结果</text>
      <t-button 
        theme="light" 
        size="small"
        bind:tap="clearResults"
        disabled="{{isRunning}}"
      >
        清空
      </t-button>
    </view>
    
    <view class="results-content">
      <view 
        class="result-item {{item.indexOf('✅') !== -1 ? 'success' : item.indexOf('❌') !== -1 ? 'error' : item.indexOf('⚠️') !== -1 ? 'warning' : ''}}"
        wx:for="{{testResults}}" 
        wx:key="index"
      >
        <text class="result-text">{{item}}</text>
      </view>
      
      <view class="empty-results" wx:if="{{testResults.length === 0}}">
        <text class="empty-text">暂无测试结果</text>
      </view>
    </view>
  </view>

  <!-- 当前测试状态 -->
  <view class="current-test" wx:if="{{isRunning}}">
    <text class="test-status">正在进行: {{currentTest}}</text>
  </view>

  <t-toast id="t-toast" />
</view>
