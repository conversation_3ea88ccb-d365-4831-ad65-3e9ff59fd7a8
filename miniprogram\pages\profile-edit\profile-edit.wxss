/* profile-edit.wxss */
.container {
  padding: 16px;
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "<PERSON><PERSON>ang SC", "<PERSON><PERSON>ang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

/* 头像区域 */
.avatar-section {
  margin-bottom: 32px;
}

.avatar-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 120px;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  border-radius: 8px;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 120px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.avatar-button:active .avatar-overlay {
  opacity: 1;
}

.avatar-text {
  color: #ffffff;
  font-size: 12px;
  text-align: center;
}

/* 昵称区域 */
.nickname-section {
  margin-bottom: 32px;
}

.nickname-input {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 16px;
  color: #333;
  background-color: #ffffff;
  box-sizing: border-box;
}

.nickname-input:focus {
  border-color: #0052d9;
}



/* 提交按钮区域 */
.submit-section {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 12px;
  }
  
  .form-section {
    padding: 20px;
  }
  
  .avatar-wrapper {
    width: 100px;
    height: 100px;
  }
  
  .avatar-button {
    width: 100px;
    height: 100px;
  }
  
  .avatar-overlay {
    width: 100px;
    height: 100px;
  }
} 