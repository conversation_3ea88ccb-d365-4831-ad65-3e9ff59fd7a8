/* avatar-test.wxss */
.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px 0;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

/* 用户信息样式 */
.user-info {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #666;
  width: 60px;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: #333;
  flex: 1;
}

/* 测试头像样式 */
.test-avatar {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin: 8px 0;
}

.avatar-url {
  font-size: 12px;
  color: #666;
  word-break: break-all;
  display: block;
  margin-top: 8px;
}

/* 测试按钮样式 */
.test-buttons {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.button-row {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.button-row .t-button {
  flex: 1;
}

/* 测试结果样式 */
.test-results {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.results-content {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border-left: 3px solid #dee2e6;
}

.result-item.success {
  background-color: #d4edda;
  border-left-color: #28a745;
}

.result-item.error {
  background-color: #f8d7da;
  border-left-color: #dc3545;
}

.result-item.warning {
  background-color: #fff3cd;
  border-left-color: #ffc107;
}

.result-text {
  font-size: 12px;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

.empty-results {
  text-align: center;
  padding: 40px 0;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 当前测试状态 */
.current-test {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #0052d9;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 1000;
}

.test-status {
  color: white;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 12px;
  }
  
  .button-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .button-row .t-button {
    flex: none;
  }
}
