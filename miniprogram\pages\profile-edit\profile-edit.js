// profile-edit.js
// 导入Toast工具函数
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

const defaultAvatarUrl = '' // 使用 TDesign Icon 替代默认头像

Page({
  data: {
    avatarUrl: defaultAvatarUrl,
    nickName: '',
    isSubmitting: false,
    pageTitle: '完善个人信息',
    pageSubtitle: '请设置您的头像和昵称',
    submitText: '完成注册',
    editProfile: false
  },

  onLoad(options) {
    // 隐藏TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }
    
    console.log('注册页面加载，参数:', options);
    console.log('初始头像URL:', this.data.avatarUrl);
    
    // 检测是否在开发者工具中
    const systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);
    
    if (systemInfo.platform === 'devtools') {
      showToast(this, { 
        message: '开发环境：头像可能无法正常显示，建议真机测试', 
        theme: 'warning',
        duration: 4000
      });
    }
    
    // 如果有传入的用户信息，使用传入的信息
    if (options.userInfo) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(options.userInfo));
        console.log('传入的用户信息:', userInfo);
        this.setData({
          avatarUrl: userInfo.avatarUrl || defaultAvatarUrl,
          nickName: userInfo.nickName || ''
        });
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    // 新增：支持资料编辑模式
    if (options.editProfile === '1') {
      this.setData({
        pageTitle: '编辑个人资料',
        pageSubtitle: '修改头像和昵称',
        submitText: '保存',
        editProfile: true
      });
      // 预填当前用户信息
      const app = getApp();
      const userInfo = app.getUserInfo && app.getUserInfo();
      if (userInfo) {
        this.setData({
          avatarUrl: userInfo.avatarUrl || defaultAvatarUrl,
          nickName: userInfo.nickName || ''
        });
      }
    }
  },

  onShow() {
    // 每次显示页面时都隐藏TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }
  },

  // 头像加载成功处理
  onAvatarLoad(e) {
    console.log('头像加载成功:', e.detail);
    console.log('加载的头像URL:', this.data.avatarUrl);
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.log('头像加载错误:', e.detail);
    console.log('当前头像URL:', this.data.avatarUrl);
    
    // 如果当前不是默认头像，则使用默认头像
    if (this.data.avatarUrl !== defaultAvatarUrl) {
      console.log('切换到默认头像');
      this.setData({
        avatarUrl: defaultAvatarUrl
      });
    }
  },

  // 选择头像
  async onChooseAvatar(e) {
    console.log('=== 头像选择开始 ===');
    console.log('选择头像事件详情:', e);
    console.log('事件详情对象:', e.detail);
    
    const { avatarUrl } = e.detail;
    console.log('提取的头像URL:', avatarUrl);
    console.log('URL类型:', typeof avatarUrl);
    console.log('URL长度:', avatarUrl ? avatarUrl.length : 0);
    
    if (!avatarUrl) {
      console.error('头像选择失败 - avatarUrl为空');
      showToast(this, { message: '头像选择失败', theme: 'error' });
      return;
    }
    
    showLoading(this, '上传头像中...');
    try {
      // 上传头像到云存储
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const cloudPath = `avatars/${timestamp}_${randomStr}.jpg`;
      
      console.log('开始上传头像到云存储:', cloudPath);
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath,
        filePath: avatarUrl
      });
      
      console.log('云存储上传结果:', uploadResult);
      
      // 获取公开访问的URL
      const publicUrl = uploadResult.fileID;
      console.log('获取到的公开URL:', publicUrl);
      
      // 设置新头像URL
      this.setData({
        avatarUrl: publicUrl
      }, () => {
        console.log('setData回调 - 设置后的avatarUrl:', this.data.avatarUrl);
      });
      
      showToast(this, { message: '头像上传成功', theme: 'success' });
      
    } catch (error) {
      console.error('=== 头像上传失败 ===');
      console.error('错误详情:', error);
      console.error('错误堆栈:', error.stack);
      console.error('错误消息:', error.message);
      console.error('错误代码:', error.code);

      showToast(this, { message: '头像上传失败，请重试', theme: 'error' });

      // 上传失败时使用默认头像
      console.log('设置默认头像:', defaultAvatarUrl);
      this.setData({
        avatarUrl: defaultAvatarUrl
      });
    } finally {
      hideToast(this);
    }
    
    console.log('=== 头像选择结束 ===');
  },

  // 昵称输入
  onNicknameInput(e) {
    console.log('昵称输入:', e.detail.value);
    this.setData({
      nickName: e.detail.value
    });
  },

  // 昵称失焦
  onNicknameBlur(e) {
    console.log('昵称失焦:', e.detail.value);
    // 微信会对输入内容进行安全检测
    // 如果未通过检测，微信会清空输入内容
    this.setData({
      nickName: e.detail.value
    });
  },

  // 提交注册/保存资料
  async handleSubmit() {
    try {
      console.log('开始提交注册...');
      console.log('当前头像URL:', this.data.avatarUrl);
      
      // 验证必填项
      if (!this.data.nickName.trim()) {
        showToast(this, { message: '请输入昵称', theme: 'error' });
        return;
      }

      this.setData({
        isSubmitting: true
      });

      if (this.data.editProfile) {
        console.log('=== 资料编辑模式保存开始 ===');

        // 资料编辑模式，直接保存到云端并同步本地
        const app = getApp();
        const userInfo = app.getUserInfo && app.getUserInfo();
        console.log('当前用户信息:', userInfo);

        const updateData = {
          nickName: this.data.nickName.trim(),
          avatarUrl: this.data.avatarUrl
        };

        console.log('准备更新的数据:', updateData);
        console.log('头像URL详情:', {
          url: updateData.avatarUrl,
          type: typeof updateData.avatarUrl,
          length: updateData.avatarUrl ? updateData.avatarUrl.length : 0,
          isCloudUrl: updateData.avatarUrl ? updateData.avatarUrl.startsWith('cloud://') : false
        });

        // 云函数更新
        console.log('调用云函数 updateUserInfo...');
        const res = await wx.cloud.callFunction({
          name: 'userManagement',
          data: {
            action: 'updateUserInfo',
            data: updateData
          }
        });

        console.log('云函数调用结果:', res);
        console.log('云函数返回详情:', JSON.stringify(res.result, null, 2));

        if (res.result && res.result.success) {
          console.log('云函数调用成功，开始更新本地存储...');

          // 更新本地存储
          const updatedUserInfo = { ...userInfo, ...updateData };
          console.log('更新后的用户信息:', updatedUserInfo);

          wx.setStorageSync('userInfo', updatedUserInfo);
          app.globalData.userInfo = updatedUserInfo;

          console.log('本地存储更新完成');
          showToast(this, { message: '保存成功', theme: 'success' });

          setTimeout(() => {
            console.log('准备返回上一页');
            wx.navigateBack({ delta: 1 });
          }, 1200);
        } else {
          console.error('云函数调用失败:', res.result);
          throw new Error(res.result?.message || '保存失败');
        }

        console.log('=== 资料编辑模式保存结束 ===');
        return;
      }

      // 获取微信登录凭证
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      // 准备用户信息
      const userInfo = {
        nickName: this.data.nickName.trim(),
        avatarUrl: this.data.avatarUrl,
        role: '讲师', // 默认角色，由管理员维护
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      };

      console.log('准备提交的用户信息:', userInfo);

      // 调用云函数进行注册
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'login',
          data: {
            userInfo: userInfo
          }
        }
      });

      if (cloudResult.result.success) {
        const finalUserInfo = {
          ...userInfo,
          openid: cloudResult.result.openid,
          role: cloudResult.result.role || '学员',
          ...cloudResult.result.userInfo
        };

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', finalUserInfo);
        
        // 更新全局数据
        const app = getApp();
        app.globalData.userInfo = finalUserInfo;
        app.globalData.isLoggedIn = true;

        showToast(this, { message: '注册成功', theme: 'success' });

        console.log('注册成功，用户信息:', finalUserInfo);

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          });
        }, 1500);

      } else {
        throw new Error(cloudResult.result.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      showToast(this, { message: '注册失败，请重试', theme: 'error' });
    } finally {
      this.setData({
        isSubmitting: false
      });
    }
  }
}); 