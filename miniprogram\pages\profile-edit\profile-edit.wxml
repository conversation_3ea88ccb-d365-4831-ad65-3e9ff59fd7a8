<!--profile-edit.wxml-->
<view class="container">
  <view class="header">
    <text class="title">{{pageTitle}}</text>
    <text class="subtitle">{{pageSubtitle}}</text>
  </view>

  <view class="form-section">
    <!-- 头像选择 -->
    <view class="avatar-section">
      <text class="section-title">头像</text>
      <view class="avatar-wrapper">
        <image 
          wx:if="{{avatarUrl}}"
          src="{{avatarUrl}}" 
          mode="aspectFill"
          class="avatar"
          binderror="onAvatarError"
          bindload="onAvatarLoad"
        />
        <view wx:else class="avatar-placeholder">
          <t-icon name="user" size="60rpx" color="#ccc"/>
        </view>
        <button class="avatar-button" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <view class="avatar-overlay">
            <text class="avatar-text">点击选择头像</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 昵称填写 -->
    <view class="nickname-section">
      <text class="section-title">昵称</text>
      <input 
        type="nickname" 
        class="nickname-input" 
        placeholder="请输入昵称"
        value="{{nickName}}"
        bind:input="onNicknameInput"
        bind:blur="onNicknameBlur"
      />
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <t-button 
      theme="primary" 
      size="large"
      block
      bind:tap="handleSubmit"
      loading="{{isSubmitting}}"
    >
      {{submitText}}
    </t-button>
  </view>
  <t-toast id="t-toast" />
</view> 